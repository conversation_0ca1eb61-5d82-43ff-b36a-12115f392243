package com.high.ayodj

import android.app.Activity
import android.content.Context
import android.util.Log
import com.android.billingclient.api.*
import com.google.firebase.functions.FirebaseFunctions
import com.google.firebase.functions.ktx.functions
import com.google.firebase.ktx.Firebase
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

class BillingClientWrapper(
    private val context: Context,
    private val onPurchaseComplete: (Purchase) -> Unit,
    private val onPurchaseFailed: (BillingResult) -> Unit
) : PurchasesUpdatedListener {

    private val functions: FirebaseFunctions = Firebase.functions
    private val coroutineScope = GlobalScope
    private var productDetailsList: List<ProductDetails>? = null

    private val billingClient: BillingClient = BillingClient.newBuilder(context)
        .setListener(this)
        .enablePendingPurchases()
        .build()

    fun startConnection() {
        billingClient.startConnection(object : BillingClientStateListener {
            override fun onBillingSetupFinished(billingResult: BillingResult) {
                if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                    Log.d("BillingClientWrapper", "Billing Client connected")
                    queryAvailableProducts()
                    queryPurchases()
                } else {
                    Log.e("BillingClientWrapper", "Billing Client connection failure: ${billingResult.debugMessage}")
                }
            }

            override fun onBillingServiceDisconnected() {
                Log.d("BillingClientWrapper", "Billing Client disconnected, trying to reconnect...")
                // Simple retry logic, consider a more robust solution for production
                coroutineScope.launch {
                    kotlinx.coroutines.delay(3000) // 3 seconds delay
                    startConnection()
                }
            }
        })
    }

    private fun queryPurchases() {
        if (!billingClient.isReady) {
            Log.e("BillingClientWrapper", "queryPurchases: BillingClient is not ready")
            return
        }

        // Query subscriptions
        val subsParams = QueryPurchasesParams.newBuilder()
            .setProductType(BillingClient.ProductType.SUBS)
            .build()

        billingClient.queryPurchasesAsync(subsParams) { billingResult, purchases ->
            if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                if (purchases.isNotEmpty()) {
                    // Just taking the first one for simplicity
                    val purchase = purchases.first()
                    if (purchase.purchaseState == Purchase.PurchaseState.PURCHASED) {
                        onPurchaseComplete(purchase)
                    }
                } else {
                    Log.d("BillingClientWrapper", "No active subscriptions found.")
                }
            } else {
                Log.e("BillingClientWrapper", "Error querying subscriptions: ${billingResult.debugMessage}")
            }
        }

        // Query in-app purchases
        val inAppParams = QueryPurchasesParams.newBuilder()
            .setProductType(BillingClient.ProductType.INAPP)
            .build()

        billingClient.queryPurchasesAsync(inAppParams) { billingResult, purchases ->
            if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                if (purchases.isNotEmpty()) {
                    val purchase = purchases.first()
                    if (purchase.purchaseState == Purchase.PurchaseState.PURCHASED) {
                        onPurchaseComplete(purchase)
                    }
                } else {
                    Log.d("BillingClientWrapper", "No active in-app purchases found.")
                }
            } else {
                Log.e("BillingClientWrapper", "Error querying in-app purchases: ${billingResult.debugMessage}")
            }
        }
    }


    fun queryAvailableProducts() {
        // Query both in-app products and subscriptions
        queryInAppProducts()
        querySubscriptions()
    }
    
    private fun queryInAppProducts() {
        val productList = listOf(
            QueryProductDetailsParams.Product.newBuilder()
                .setProductId("upload_with_month_access") // Match your Play Console product ID
                .setProductType(BillingClient.ProductType.INAPP)
                .build()
        )

        val params = QueryProductDetailsParams.newBuilder()
            .setProductList(productList)
            .build()

        billingClient.queryProductDetailsAsync(params) { billingResult, productDetailsList ->
            if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                if (!productDetailsList.isNullOrEmpty()) {
                    Log.d("BillingClientWrapper", "In-app product details loaded: ${productDetailsList.map { it.name }}")
                } else {
                    Log.d("BillingClientWrapper", "No in-app product details found.")
                }
            } else {
                Log.e("BillingClientWrapper", "Failed to query in-app product details: ${billingResult.debugMessage}")
            }
        }
    }
    
    private fun querySubscriptions() {
        val productList = listOf(
            QueryProductDetailsParams.Product.newBuilder()
                .setProductId("premium_monthly_subscription") // Match your Play Console subscription ID
                .setProductType(BillingClient.ProductType.SUBS)
                .build()
        )

        val params = QueryProductDetailsParams.newBuilder()
            .setProductList(productList)
            .build()

        billingClient.queryProductDetailsAsync(params) { billingResult, productDetailsList ->
            if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                if (!productDetailsList.isNullOrEmpty()) {
                    this.productDetailsList = productDetailsList
                    Log.d("BillingClientWrapper", "Subscription product details loaded: ${productDetailsList.map { it.name }}")
                } else {
                    Log.d("BillingClientWrapper", "No subscription product details found.")
                }
            } else {
                Log.e("BillingClientWrapper", "Failed to query subscription product details: ${billingResult.debugMessage}")
            }
        }
    }


    fun launchUploadAccessPurchase(activity: Activity) {
        launchPurchaseFlow(activity, "upload_with_month_access", BillingClient.ProductType.INAPP)
    }
    
    fun launchPremiumSubscription(activity: Activity) {
        launchPurchaseFlow(activity, "premium_monthly_subscription", BillingClient.ProductType.SUBS)
    }

    private fun launchPurchaseFlow(activity: Activity, productId: String, productType: String) {
        val productDetailsParams = QueryProductDetailsParams.Product.newBuilder()
            .setProductId(productId)
            .setProductType(productType)
            .build()

        val params = QueryProductDetailsParams.newBuilder()
            .setProductList(listOf(productDetailsParams))
            .build()

        billingClient.queryProductDetailsAsync(params) { billingResult, productDetailsList ->
            if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                val productDetails = productDetailsList.firstOrNull()
                if (productDetails != null) {
                    val productDetailsParamsList = if (productType == BillingClient.ProductType.SUBS) {
                        // For subscription with free trial
                        val offerToken = productDetails.subscriptionOfferDetails?.firstOrNull()?.offerToken ?: ""
                        listOf(
                            BillingFlowParams.ProductDetailsParams.newBuilder()
                                .setProductDetails(productDetails)
                                .setOfferToken(offerToken)
                                .build()
                        )
                    } else {
                        // For one-time in-app purchase
                        listOf(
                            BillingFlowParams.ProductDetailsParams.newBuilder()
                                .setProductDetails(productDetails)
                                .build()
                        )
                    }

                    val billingFlowParams = BillingFlowParams.newBuilder()
                        .setProductDetailsParamsList(productDetailsParamsList)
                        .build()

                    val billingResult = billingClient.launchBillingFlow(activity, billingFlowParams)
                    if (billingResult.responseCode != BillingClient.BillingResponseCode.OK) {
                        Log.e("BillingClientWrapper", "Failed to launch billing flow: ${billingResult.debugMessage}")
                        onPurchaseFailed(billingResult)
                    }
                } else {
                    Log.e("BillingClientWrapper", "Product details not found for $productId")
                }
            } else {
                Log.e("BillingClientWrapper", "Failed to query product details for $productId: ${billingResult.debugMessage}")
            }
        }
    }


    override fun onPurchasesUpdated(billingResult: BillingResult, purchases: MutableList<Purchase>?) {
        if (billingResult.responseCode == BillingClient.BillingResponseCode.OK && purchases != null) {
            for (purchase in purchases) {
                handlePurchase(purchase)
            }
        } else if (billingResult.responseCode == BillingClient.BillingResponseCode.USER_CANCELED) {
            Log.d("BillingClientWrapper", "User cancelled the purchase")
        } else {
            Log.e("BillingClientWrapper", "Purchase updated error: ${billingResult.debugMessage}")
            onPurchaseFailed(billingResult)
        }
    }

    private fun handlePurchase(purchase: Purchase) {
        if (purchase.purchaseState == Purchase.PurchaseState.PURCHASED) {
            if (!purchase.isAcknowledged) {
                // Acknowledge the purchase with Google Play
                val acknowledgePurchaseParams = AcknowledgePurchaseParams.newBuilder()
                    .setPurchaseToken(purchase.purchaseToken)
                    .build()
                billingClient.acknowledgePurchase(acknowledgePurchaseParams) { billingResult ->
                    if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                        // Purchase acknowledged, now verify on the backend
                        Log.d("BillingClientWrapper", "Purchase acknowledged. Verifying on backend.")
                        // Here is where we call our new function
                        verifyPurchaseOnBackend(purchase)
                    }
                }
            }
        }
    }

    private fun verifyPurchaseOnBackend(purchase: Purchase) {
        val data = hashMapOf(
            "packageName" to context.packageName,
            "subscriptionId" to purchase.products.first(),
            "token" to purchase.purchaseToken
        )

        functions
            .getHttpsCallable("verifyPurchase") // This MUST match the name of your Cloud Function
            .call(data)
            .addOnCompleteListener { task ->
                if (!task.isSuccessful) {
                    val e = task.exception
                    Log.e("BillingClientWrapper", "Backend verification failed.", e)
                    // Optionally, handle the error (e.g., show a message to the user)
                    return@addOnCompleteListener
                }

                // Backend verification was successful!
                // The Cloud Function has already updated Firestore.
                // Your app's UI should automatically update if it's listening to Firestore.
                val result = task.result?.data as? Map<*, *>
                val message = result?.get("message")?.toString() ?: "Verification successful."
                Log.d("BillingClientWrapper", "Backend verification success: $message")
            }
    }

    fun exchangeCodeForTokens(code: String) {
        val data = hashMapOf("code" to code)
        functions.getHttpsCallable("getTokens")
            .call(data)
            .addOnCompleteListener { task ->
                if (!task.isSuccessful) {
                    val e = task.exception
                    Log.e("BillingClientWrapper", "Failed to exchange code for tokens", e)
                    return@addOnCompleteListener
                }
                val result = task.result?.data as? Map<*, *>
                Log.d("BillingClientWrapper", "Tokens received: $result")
            }
    }
}
