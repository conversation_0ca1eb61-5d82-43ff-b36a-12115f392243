package com.high.ayodj.monetization.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.high.ayodj.monetization.Track
import com.high.ayodj.monetization.SubscriptionStatus
import com.high.ayodj.monetization.TrackState

/**
 * UI component for displaying the track list with monetization features
 */
@Composable
fun TrackListUI(
    tracks: List<Track>,
    subscriptionStatus: SubscriptionStatus,
    downloadingTracks: Set<String>,
    downloadErrors: Map<String, String>,
    onTrackPlay: (Track) -> Unit,
    onTrackSave: (Track) -> Unit,
    onUpgradeClicked: () -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // Header
        item {
            TrackListHeader(subscriptionStatus = subscriptionStatus)
        }
        
        // Track items - released tracks first (newest first), then unreleased tracks (closest to release first)
        items(tracks.sortedWith(
            compareBy<Track> { it.isYetToBeReleased() }
                .thenBy { if (it.isYetToBeReleased()) it.releaseTimestamp else -it.releaseTimestamp }
        )) { track ->
            TrackListItem(
                track = track,
                subscriptionStatus = subscriptionStatus,
                isDownloading = downloadingTracks.contains(track.id),
                downloadError = downloadErrors[track.id],
                onPlay = { onTrackPlay(track) },
                onSave = { onTrackSave(track) },
                onUpgrade = onUpgradeClicked
            )
        }
        
        // Empty state or upgrade prompt
        if (tracks.isEmpty()) {
            item {
                EmptyTrackList(
                    subscriptionStatus = subscriptionStatus,
                    onUpgrade = onUpgradeClicked
                )
            }
        }
    }
}

@Composable
private fun TrackListHeader(subscriptionStatus: SubscriptionStatus) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (subscriptionStatus.hasFullAccess()) 
                MaterialTheme.colorScheme.primaryContainer 
            else MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    imageVector = when (subscriptionStatus) {
                        SubscriptionStatus.FREE -> Icons.Default.PlayArrow
                        SubscriptionStatus.TRIAL -> Icons.Default.Star
                        SubscriptionStatus.UPLOAD_WITH_MONTH -> Icons.Default.Star
                        SubscriptionStatus.UPLOAD_EXPIRED -> Icons.Default.Person
                        SubscriptionStatus.FULL_PREMIUM -> Icons.Default.Star
                    },
                    contentDescription = null,
                    tint = when (subscriptionStatus) {
                        SubscriptionStatus.FREE -> MaterialTheme.colorScheme.onSurfaceVariant
                        SubscriptionStatus.TRIAL -> MaterialTheme.colorScheme.primary
                        SubscriptionStatus.UPLOAD_WITH_MONTH -> MaterialTheme.colorScheme.primary
                        SubscriptionStatus.UPLOAD_EXPIRED -> MaterialTheme.colorScheme.secondary
                        SubscriptionStatus.FULL_PREMIUM -> MaterialTheme.colorScheme.primary
                    }
                )
                
                Text(
                    text = subscriptionStatus.getDisplayName(),
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
            }
            
            Text(
                text = when (subscriptionStatus) {
                    SubscriptionStatus.FREE -> "Limited access to recent releases"
                    SubscriptionStatus.TRIAL -> "Full track access for 2 weeks (streaming only)"
                    SubscriptionStatus.UPLOAD_WITH_MONTH -> "File imports + full premium access this month"
                    SubscriptionStatus.UPLOAD_EXPIRED -> "File imports only - upgrade for full access"
                    SubscriptionStatus.FULL_PREMIUM -> "Unlimited access to all tracks + save feature"
                },
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun TrackListItem(
    track: Track,
    subscriptionStatus: SubscriptionStatus,
    isDownloading: Boolean,
    downloadError: String?,
    onPlay: () -> Unit,
    onSave: () -> Unit,
    onUpgrade: () -> Unit
) {
    val trackState = when {
        track.isYetToBeReleased() -> TrackState.YetToBeReleased(track.getTimeUntilReleaseFormatted())
        !track.isVisibleTo(subscriptionStatus) -> TrackState.Expired()
        !track.isUnlocked -> TrackState.Locked
        else -> TrackState.Available
    }
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .alpha(if (trackState is TrackState.Expired) 0.6f else 1.0f),
        colors = CardDefaults.cardColors(
            containerColor = when (trackState) {
                is TrackState.Available -> MaterialTheme.colorScheme.surface
                is TrackState.Locked -> MaterialTheme.colorScheme.surfaceVariant
                is TrackState.Expired -> MaterialTheme.colorScheme.errorContainer
                is TrackState.YetToBeReleased -> MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.7f)
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Track info header
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = track.title,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    Text(
                        text = track.artist,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    if (track.durationSeconds > 0) {
                        Text(
                            text = formatDuration(track.durationSeconds),
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            maxLines = 1
                        )
                    }
                }
                
                // Status indicator
                TrackStatusIndicator(trackState = trackState, track = track)
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Action buttons
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Play button
                Button(
                    onClick = if (trackState is TrackState.Available) onPlay else { {} },
                    enabled = trackState is TrackState.Available,
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(
                        imageVector = when (trackState) {
                            is TrackState.Available -> Icons.Default.CheckCircle // Available/unlocked state
                            else -> Icons.Default.Lock // Locked state for all others
                        },
                        contentDescription = null
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = when (trackState) {
                            is TrackState.Available -> "Play"
                            is TrackState.Locked -> "Locked"
                            is TrackState.Expired -> "Expired"
                            is TrackState.YetToBeReleased -> "Soon"
                        }
                    )
                }
                
                // Save button
                SaveButton(
                    track = track,
                    subscriptionStatus = subscriptionStatus,
                    trackState = trackState,
                    isDownloading = isDownloading,
                    downloadError = downloadError,
                    onSave = onSave,
                    onUpgrade = onUpgrade,
                    modifier = Modifier.weight(1f)
                )
            }
            
            // Release countdown for unreleased tracks
            if (trackState is TrackState.YetToBeReleased) {
                Spacer(modifier = Modifier.height(8.dp))
                ReleaseCountdown(trackState.releaseCountdown)
            }
            
            // Expiration info for free users
            if (subscriptionStatus == SubscriptionStatus.FREE && track.isVisibleTo(subscriptionStatus)) {
                Spacer(modifier = Modifier.height(8.dp))
                ExpirationInfo(track = track)
            }
            
            // Download error display
            if (downloadError != null) {
                Spacer(modifier = Modifier.height(8.dp))
                DownloadError(error = downloadError, onRetry = onSave)
            }
            
            // Upgrade prompt for expired tracks
            if (trackState is TrackState.Expired) {
                Spacer(modifier = Modifier.height(8.dp))
                UpgradePrompt(onUpgrade = onUpgrade)
            }
        }
    }
}

@Composable
private fun TrackStatusIndicator(trackState: TrackState, track: Track) {
    Box(
        modifier = Modifier
            .size(40.dp)
            .clip(CircleShape)
            .background(
                when (trackState) {
                    is TrackState.Available -> MaterialTheme.colorScheme.primary
                    is TrackState.Locked -> MaterialTheme.colorScheme.outline
                    is TrackState.Expired -> MaterialTheme.colorScheme.error
                    is TrackState.YetToBeReleased -> MaterialTheme.colorScheme.secondary
                }
            ),
        contentAlignment = Alignment.Center
    ) {
        Icon(
            imageVector = when (trackState) {
                is TrackState.Available -> Icons.Default.Check // Simple unlocked/available checkmark
                else -> Icons.Default.Lock // Locked state for all others
            },
            contentDescription = null,
            tint = when (trackState) {
                is TrackState.Available -> MaterialTheme.colorScheme.onPrimary
                is TrackState.Locked -> MaterialTheme.colorScheme.onSurface
                is TrackState.Expired -> MaterialTheme.colorScheme.onError
                is TrackState.YetToBeReleased -> MaterialTheme.colorScheme.onSecondary
            },
            modifier = Modifier.size(20.dp)
        )
    }
}

@Composable
private fun SaveButton(
    track: Track,
    subscriptionStatus: SubscriptionStatus,
    trackState: TrackState,
    isDownloading: Boolean,
    downloadError: String?,
    onSave: () -> Unit,
    onUpgrade: () -> Unit,
    modifier: Modifier = Modifier
) {
    val canSave = subscriptionStatus.canSaveTracks() && trackState is TrackState.Available
    val isSaved = track.localFilePath != null
    val hasError = downloadError != null
    
    Button(
        onClick = when {
            hasError -> onSave // Retry on error
            canSave && !isSaved && !isDownloading -> onSave
            !canSave && subscriptionStatus.canSaveTracks() -> onUpgrade // Only show upgrade for premium-capable users
            else -> { {} }
        },
        enabled = when {
            isDownloading -> false // Disabled during download
            trackState is TrackState.Locked -> false // Disabled for locked tracks
            trackState is TrackState.Expired -> false // Disabled for expired tracks  
            trackState is TrackState.YetToBeReleased -> false // Disabled for unreleased tracks
            hasError -> true // Allow retry on error
            canSave -> true // Allow save for available tracks
            !canSave && subscriptionStatus.canSaveTracks() -> true // Allow upgrade button
            else -> false // Disabled for all other cases
        },
        colors = ButtonDefaults.buttonColors(
            containerColor = when {
                hasError -> MaterialTheme.colorScheme.error
                isSaved -> MaterialTheme.colorScheme.secondary
                isDownloading -> MaterialTheme.colorScheme.outline
                canSave -> MaterialTheme.colorScheme.primary
                else -> MaterialTheme.colorScheme.outline
            }
        ),
        modifier = modifier
    ) {
        if (isDownloading) {
            CircularProgressIndicator(
                modifier = Modifier.size(16.dp),
                strokeWidth = 2.dp,
                color = MaterialTheme.colorScheme.onSurface
            )
        } else {
            Icon(
                imageVector = when {
                    hasError -> Icons.Default.Refresh
                    isSaved -> Icons.Default.CheckCircle
                    trackState is TrackState.YetToBeReleased -> Icons.Default.Lock
                    canSave -> Icons.Default.Add
                    else -> Icons.Default.Lock
                },
                contentDescription = null,
                modifier = Modifier.size(16.dp)
            )
        }
        Spacer(modifier = Modifier.width(4.dp))
        Text(
            text = when {
                hasError -> "Retry"
                isSaved -> "Saved"
                isDownloading -> "Saving..."
                trackState is TrackState.YetToBeReleased -> "Soon"
                canSave -> "Save"
                else -> "Premium"
            },
            fontSize = 12.sp
        )
    }
}

@Composable
private fun ExpirationInfo(track: Track) {
    val timeRemaining = track.getTimeRemainingFormatted()
    
    if (timeRemaining != "Expired") {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Warning,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.size(16.dp)
            )
            Text(
                text = "Expires in $timeRemaining",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun UpgradePrompt(onUpgrade: () -> Unit) {
    OutlinedButton(
        onClick = onUpgrade,
        modifier = Modifier.fillMaxWidth()
    ) {
        Icon(
            imageVector = Icons.Default.Star,
            contentDescription = null
        )
        Spacer(modifier = Modifier.width(4.dp))
        Text("Upgrade for backlog access")
    }
}

@Composable
private fun EmptyTrackList(
    subscriptionStatus: SubscriptionStatus,
    onUpgrade: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Icon(
                imageVector = Icons.Default.PlayArrow,
                contentDescription = null,
                modifier = Modifier.size(48.dp),
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Text(
                text = "No tracks available",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Text(
                text = when (subscriptionStatus) {
                    SubscriptionStatus.FREE -> "New tracks drop every Thursday at 4:20 PM"
                    SubscriptionStatus.TRIAL -> "New tracks drop every Thursday at 4:20 PM"
                    SubscriptionStatus.UPLOAD_WITH_MONTH -> "Check back soon for new releases"
                    SubscriptionStatus.UPLOAD_EXPIRED -> "New tracks drop every Thursday at 4:20 PM"
                    SubscriptionStatus.FULL_PREMIUM -> "Check back soon for new releases"
                },
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            if (!subscriptionStatus.hasFullAccess()) {
                Button(
                    onClick = onUpgrade
                ) {
                    Icon(imageVector = Icons.Default.Star, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("Upgrade to Premium")
                }
            }
        }
    }
}

@Composable
private fun ReleaseCountdown(countdown: String) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.7f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = Icons.Default.Info,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSecondaryContainer,
                modifier = Modifier.size(16.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "Releases in $countdown",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSecondaryContainer,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

@Composable
private fun DownloadError(error: String, onRetry: () -> Unit) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.7f)
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Warning,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onErrorContainer,
                    modifier = Modifier.size(16.dp)
                )
                Text(
                    text = "Download failed: $error",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onErrorContainer,
                    modifier = Modifier.weight(1f)
                )
            }
            Spacer(modifier = Modifier.height(8.dp))
            OutlinedButton(
                onClick = onRetry,
                modifier = Modifier.align(Alignment.End),
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = MaterialTheme.colorScheme.onErrorContainer
                ),
                border = BorderStroke(1.dp, MaterialTheme.colorScheme.onErrorContainer.copy(alpha = 0.5f))
            ) {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = null,
                    modifier = Modifier.size(14.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text("Retry", fontSize = 12.sp)
            }
        }
    }
}

/**
 * Helper function to format duration from seconds to MM:SS format
 */
private fun formatDuration(durationSeconds: Int): String {
    val minutes = durationSeconds / 60
    val seconds = durationSeconds % 60
    return "%d:%02d".format(minutes, seconds)
}
