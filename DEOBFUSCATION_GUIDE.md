# Deobfuscation File (Mapping File) Guide

## What is a Deobfuscation File?

When you build a release APK with `isMinifyEnabled = true`, ProGuard/R8 obfuscates your code by renaming classes, methods, and variables to shorter names. The **mapping file** contains the translation between original names and obfuscated names.

## Where to Find Your Mapping File

After building a release APK, the mapping file is generated at:
```
app/build/outputs/mapping/release/mapping.txt
```

## How to Upload to Google Play Console

1. **Build your release APK:**
   ```bash
   ./gradlew assembleRelease
   ```

2. **Locate the mapping file:**
   - Path: `app/build/outputs/mapping/release/mapping.txt`

3. **Upload to Google Play Console:**
   - Go to Google Play Console
   - Select your app
   - Go to "App bundle explorer" or "Artifact library"
   - Find your APK/Bundle version
   - Click "Upload deobfuscation file"
   - Upload the `mapping.txt` file

## Automated Upload (Recommended)

Add this to your `app/build.gradle.kts` for automatic upload:

```kotlin
android {
    // ... existing config
    
    buildTypes {
        release {
            // ... existing config
            
            // Optional: Automatically upload mapping files
            postprocessing {
                removeUnusedCode = true
                removeUnusedResources = true
                obfuscate = true
                optimizeCode = true
                proguardFile("proguard-rules.pro")
            }
        }
    }
}
```

## What Each File Does

### mapping.txt
- **Purpose:** Maps obfuscated names back to original names
- **Used for:** Crash report deobfuscation
- **Upload to:** Google Play Console

### seeds.txt
- **Purpose:** Lists classes and members that were kept (not obfuscated)
- **Used for:** Debugging ProGuard rules

### usage.txt
- **Purpose:** Lists dead code that was removed
- **Used for:** Optimization analysis

## Important Notes

1. **Keep mapping files:** Store them for each release version
2. **File structure:** One mapping file per app version
3. **Crash reports:** Without the mapping file, crash reports are unreadable
4. **Size:** Enables significant APK size reduction (often 20-40% smaller)

## Testing Obfuscation

1. **Build release APK:**
   ```bash
   ./gradlew assembleRelease
   ```

2. **Install and test:**
   ```bash
   adb install app/build/outputs/apk/release/app-release.apk
   ```

3. **Check mapping file exists:**
   ```bash
   ls app/build/outputs/mapping/release/
   ```

## Troubleshooting

If you get runtime crashes after enabling obfuscation:

1. Check your ProGuard rules in `proguard-rules.pro`
2. Add `-keep` rules for classes accessed via reflection
3. Test thoroughly before releasing
4. Use `./gradlew assembleRelease` to generate mapping files

## Current Configuration

- ✅ Release builds: Obfuscation **ENABLED**
- ✅ Debug builds: Obfuscation **DISABLED** (for easier debugging)
- ✅ Resource shrinking: **ENABLED** (removes unused resources)
- ✅ Mapping file generation: **AUTOMATIC**

Your app is now configured to generate mapping files automatically when you build release APKs.
