package com.high.ayodj.monetization

import android.content.res.AssetManager
import android.media.MediaMetadataRetriever
import android.util.Log
import org.jaudiotagger.audio.AudioFileIO
import org.jaudiotagger.tag.FieldKey
import java.io.File
import java.util.*

/**
 * Extracts metadata from MP3 files in the new batch-week-year format
 * Primary source for track information including download URLs from comments
 */
class MP3MetadataExtractor {
    
    companion object {
        private const val TAG = "MP3MetadataExtractor"
        private val FILENAME_PATTERN = Regex("""(\d{3})-(\d{2})-(\d{2})([ab])\.mp3""")
    }
    
    data class TrackMetadata(
        val batch: Int,
        val week: Int,
        val year: Int,
        val variant: String,
        val title: String,
        val artist: String,
        val downloadUrl: String?,
        val durationSeconds: Int
    )
    
    /**
     * Extract all metadata from an MP3 file in assets
     */
    fun extractTrackData(filename: String, assetManager: AssetManager): TrackMetadata? {
        try {
            // Parse filename for batch-week-year info
            val (batch, week, year, variant) = parseBatchWeekYear(filename) ?: return null
            
            // Create temporary file to read metadata
            val tempFile = File.createTempFile("track_meta_", ".mp3")
            
            try {
                // Copy asset to temp file
                assetManager.open("weekly/$filename").use { inputStream ->
                    tempFile.outputStream().use { outputStream ->
                        inputStream.copyTo(outputStream)
                    }
                }
                
                // Extract metadata using MediaMetadataRetriever
                val mmr = MediaMetadataRetriever()
                mmr.setDataSource(tempFile.absolutePath)
                
                val title = mmr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_TITLE) 
                    ?: generateTitle(batch, week, year, variant)
                val artist = mmr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_ARTIST) 
                    ?: "AyoDJ Collective"
                val duration = mmr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)?.toIntOrNull() ?: 0
                
                mmr.release()
                
                // Extract download URL from MP3 ID3 comment tag
                val downloadUrl = extractDownloadUrlFromFile(tempFile)
                
                Log.d(TAG, "Extracted metadata for $filename: title=$title, artist=$artist, url=$downloadUrl")
                
                return TrackMetadata(
                    batch = batch,
                    week = week,
                    year = year,
                    variant = variant,
                    title = title,
                    artist = artist,
                    downloadUrl = downloadUrl,
                    durationSeconds = duration / 1000
                )
                
            } finally {
                tempFile.delete()
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error extracting metadata from $filename", e)
            return null
        }
    }
    
    /**
     * Parse batch-week-year format: XXX-WW-YY[a/b].mp3
     */
    fun parseBatchWeekYear(filename: String): BatchWeekYear? {
        val match = FILENAME_PATTERN.find(filename) ?: return null
        val (batchStr, weekStr, yearStr, variant) = match.destructured
        
        try {
            val batch = batchStr.toInt()
            val week = weekStr.toInt()
            val year = 2000 + yearStr.toInt() // Convert 25 to 2025
            
            return BatchWeekYear(batch, week, year, variant)
        } catch (e: NumberFormatException) {
            Log.e(TAG, "Error parsing filename: $filename", e)
            return null
        }
    }
    
    /**
     * Extract Google Drive download URL from MP3 comment metadata using JAudioTagger
     */
    private fun extractDownloadUrlFromFile(file: File): String? {
        return try {
            val audioFile = AudioFileIO.read(file)
            val tag = audioFile.tag
            
            // Try to get comment from ID3v2 COMM frame
            val comment = tag?.getFirst(FieldKey.COMMENT)
            Log.d(TAG, "Found comment in MP3: $comment")
            
            extractDownloadUrl(comment)
        } catch (e: Exception) {
            Log.w(TAG, "Could not read ID3 comment from file: ${file.name}", e)
            null
        }
    }
    
    /**
     * Extract Google Drive download URL from MP3 comment metadata
     */
    private fun extractDownloadUrl(comment: String?): String? {
        if (comment.isNullOrBlank()) return null
        
        // Look for various Google Drive URL patterns
        val patterns = listOf(
            // Direct download URL
            Regex("""https://drive\.google\.com/uc\?export=download&id=[a-zA-Z0-9_-]+"""),
            // File view URL - we'll convert to download URL
            Regex("""https://drive\.google\.com/file/d/([a-zA-Z0-9_-]+)/view"""),
            // Open URL - we'll convert to download URL  
            Regex("""https://drive\.google\.com/open\?id=([a-zA-Z0-9_-]+)""")
        )
        
        for (pattern in patterns) {
            val match = pattern.find(comment)
            if (match != null) {
                val url = match.value
                return if (url.contains("uc?export=download")) {
                    // Already a download URL
                    url
                } else {
                    // Convert to download URL format
                    val fileId = match.groupValues.getOrNull(1)
                    if (fileId != null) {
                        "https://drive.google.com/uc?export=download&id=$fileId"
                    } else {
                        url
                    }
                }
            }
        }
        
        return null
    }
    
    /**
     * Generate a title if none exists in metadata
     */
    private fun generateTitle(batch: Int, week: Int, year: Int, variant: String): String {
        return "AyoDJ Drop B${batch.toString().padStart(3, '0')} W$week ${variant.uppercase()}"
    }
    
    /**
     * Calculate release timestamp from week of year
     */
    fun calculateReleaseTimestamp(week: Int, year: Int): Long {
        val calendar = Calendar.getInstance().apply {
            set(Calendar.YEAR, year)
            set(Calendar.WEEK_OF_YEAR, week)
            set(Calendar.DAY_OF_WEEK, Calendar.THURSDAY)
            set(Calendar.HOUR_OF_DAY, 16) // 4 PM
            set(Calendar.MINUTE, 20) // 4:20 PM
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }
        return calendar.timeInMillis
    }
    
    /**
     * Extract Google Drive download URL from MP3 comment metadata from asset
     */
    fun extractDownloadUrl(assetPath: String, assetManager: AssetManager): String? {
        return try {
            // Create temporary file to read metadata
            val tempFile = File.createTempFile("url_extract_", ".mp3")
            
            try {
                // Copy asset to temp file
                assetManager.open(assetPath).use { inputStream ->
                    tempFile.outputStream().use { outputStream ->
                        inputStream.copyTo(outputStream)
                    }
                }
                
                // Extract URL from temp file
                extractDownloadUrlFromFile(tempFile)
                
            } finally {
                // Clean up temp file
                if (tempFile.exists()) {
                    tempFile.delete()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to extract download URL from asset: $assetPath", e)
            null
        }
    }
}

/**
 * Data class for parsed filename components
 */
data class BatchWeekYear(
    val batch: Int,
    val week: Int,
    val year: Int,
    val variant: String
)
