# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Keep line numbers for crash reports
-keepattributes SourceFile,LineNumberTable
-renamesourcefileattribute SourceFile

# Keep all annotations
-keepattributes *Annotation*

# Keep native methods (for your audio processing)
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep Firebase classes
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }

# Keep your main activity and service classes
-keep class com.high.ayodj.MainActivity { *; }
-keep class com.high.ayodj.NotificationService { *; }
-keep class com.high.ayodj.monetization.** { *; }

# Keep data classes and models
-keep class com.high.ayodj.**.Track { *; }
-keep class com.high.ayodj.**.TrackMetadata { *; }

# Keep Compose classes
-keep class androidx.compose.** { *; }

# Keep audio-related classes (for your DJ functionality)
-keep class android.media.** { *; }

# Keep billing classes
-keep class com.android.billingclient.** { *; }

# WorkManager (if using the WorkManager alternative)
-keep class androidx.work.** { *; }

# Keep Gson/JSON serialization (if you use it)
-keepattributes Signature
-keepattributes *Annotation*
-keep class sun.misc.Unsafe { *; }
-keep class com.google.gson.** { *; }

# Keep your custom classes that might be accessed via reflection
-keep class com.high.ayodj.** { *; }