package com.high.ayodj.monetization

import android.content.Context
import android.content.res.AssetManager
import android.util.Log
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.io.BufferedReader
import java.io.InputStreamReader

/**
 * Manages batch downloads using CSV metadata
 * Secondary source for download URLs and batch operations
 */
class BatchDownloadManager(private val context: Context) {
    
    companion object {
        private const val TAG = "BatchDownloadManager"
        private const val CSV_FILENAME = "metadata.csv"
    }
    
    data class DownloadProgress(
        val filename: String,
        val progress: Float,
        val isComplete: Boolean,
        val error: String? = null
    )
    
    private var csvData: Map<String, String> = emptyMap()
    
    init {
        loadCsvData()
    }
    
    /**
     * Load CSV data from assets
     */
    private fun loadCsvData() {
        try {
            val assetManager = context.assets
            val csvContent = assetManager.open("weekly/$CSV_FILENAME").use { inputStream ->
                BufferedReader(InputStreamReader(inputStream)).readText()
            }
            csvData = parseCsvContent(csvContent)
            Log.i(TAG, "Loaded CSV data with ${csvData.size} entries")
        } catch (e: Exception) {
            Log.e(TAG, "Error loading CSV data", e)
        }
    }
    
    /**
     * Parse CSV content into filename->URL map
     */
    private fun parseCsvContent(content: String): Map<String, String> {
        val result = mutableMapOf<String, String>()
        val lines = content.split('\n').map { it.trim() }
        
        if (lines.isEmpty()) return result
        
        // Skip header row
        lines.drop(1).forEach { line ->
            if (line.isNotBlank()) {
                val parts = line.split(',', limit = 2)
                if (parts.size == 2) {
                    val filename = parts[0].trim()
                    val url = parts[1].trim()
                    result[filename] = url
                }
            }
        }
        
        Log.d(TAG, "Parsed CSV: ${result.keys}")
        return result
    }
    
    /**
     * Get fallback download URL from CSV
     */
    fun getFallbackUrl(filename: String): String? {
        return csvData[filename]
    }
    
    /**
     * Get all tracks for a specific batch
     */
    fun getTracksForBatch(batchNumber: Int): List<String> {
        val batchPrefix = batchNumber.toString().padStart(3, '0')
        return csvData.keys.filter { it.startsWith(batchPrefix) }
    }
    
    /**
     * Download entire batch with progress tracking
     */
    fun downloadBatch(batchNumber: Int): Flow<DownloadProgress> = flow {
        val tracksInBatch = getTracksForBatch(batchNumber)
        Log.i(TAG, "Starting batch $batchNumber download with ${tracksInBatch.size} tracks")
        
        tracksInBatch.forEachIndexed { index, filename ->
            try {
                emit(DownloadProgress(filename, 0f, false))
                
                // Simulate download progress (replace with actual download logic)
                val url = csvData[filename]
                if (url != null) {
                    // TODO: Implement actual file download
                    for (progress in 1..10) {
                        emit(DownloadProgress(filename, progress / 10f, false))
                        kotlinx.coroutines.delay(100) // Simulate download time
                    }
                    emit(DownloadProgress(filename, 1f, true))
                    Log.d(TAG, "Downloaded $filename from batch $batchNumber")
                } else {
                    emit(DownloadProgress(filename, 0f, true, "No URL found"))
                }
                
            } catch (e: Exception) {
                emit(DownloadProgress(filename, 0f, true, e.message))
                Log.e(TAG, "Error downloading $filename", e)
            }
        }
    }
    
    /**
     * Check if CSV contains data for a filename
     */
    fun hasUrl(filename: String): Boolean {
        return csvData.containsKey(filename)
    }
    
    /**
     * Get all available batches from CSV
     */
    fun getAvailableBatches(): List<Int> {
        return csvData.keys.mapNotNull { filename ->
            val batchStr = filename.substring(0, 3)
            batchStr.toIntOrNull()
        }.distinct().sorted()
    }
    
    /**
     * Reload CSV data (useful for updates)
     */
    fun reloadCsvData() {
        loadCsvData()
    }
}
