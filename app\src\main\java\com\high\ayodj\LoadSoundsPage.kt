package com.high.ayodj

import androidx.compose.runtime.Composable
import com.high.ayodj.ui.LoadSoundsPage as UILoadSoundsPage

@Composable
fun LoadSoundsPage(
    viewModel: AppViewModel,
    soundType: SoundType,
    onDismiss: () -> Unit
) {
    val soundList = when (soundType) {
        SoundType.PLATTER_SAMPLE -> viewModel.userPlatterSamplesList
        SoundType.MUSIC_TRACK -> viewModel.userMusicTracksList
    }

    UILoadSoundsPage(
        soundList = soundList,
        onAddItemClick = {
            viewModel.attemptFileLoad(soundType)
        },
        onToggleSave = { index ->
            viewModel.toggleSaveForSoundItem(index, soundType)
        },
        onLoadClick = {
            viewModel.processLoadAction(soundType)
        },
        onCancelClick = {
            viewModel.cancelLoadAction(soundType)
            onDismiss()
        },
        onSettingsClick = {
            viewModel.openSettingsFromLoadScreen()
        },
        onTrackListClick = {
            viewModel.showTracksList()
        },
        maxItems = AppViewModel.MAX_USER_SOUNDS,
        listFullErrorMessage = null, // You can implement error state if needed
        subscriptionStatus = viewModel.getSubscriptionStatus()
    )
}
