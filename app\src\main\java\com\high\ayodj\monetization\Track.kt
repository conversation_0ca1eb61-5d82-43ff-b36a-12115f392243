package com.high.ayodj.monetization

import java.util.concurrent.TimeUnit

/**
 * Represents a batch track release in the monetization system
 * New format: batch-week-year[a/b].mp3 (e.g., 001-27-25a.mp3)
 */
data class Track(
    val id: String,
    val filename: String,
    val title: String,
    val artist: String = "AyoDJ Collective",
    val batch: Int, // Batch number (001, 002, etc.)
    val week: Int, // Week of year (1-52)
    val year: Int, // Full year (2025)
    val variant: String, // "a" or "b"
    val releaseTimestamp: Long,
    val isUnlocked: Boolean = false,
    val isPremiumOnly: Boolean = false,
    val notificationSent: Boolean = false,
    val localFilePath: String? = null,
    val downloadUrl: String? = null,
    val fileSizeBytes: Long = 0L,
    val durationSeconds: Int = 0
) {
    companion object {
        const val FREE_VISIBILITY_DAYS = 14L
        const val PREMIUM_VISIBILITY_DAYS = 7L
    }
    
    /**
     * Check if this track is visible based on subscription status
     */
    fun isVisibleTo(subscriptionStatus: SubscriptionStatus): Boolean {
        val currentTime = System.currentTimeMillis()
        
        return when (subscriptionStatus) {
            SubscriptionStatus.FREE -> {
                // Free users get limited 14-day window per track
                (currentTime - releaseTimestamp) <= TimeUnit.DAYS.toMillis(FREE_VISIBILITY_DAYS)
            }
            SubscriptionStatus.TRIAL -> true   // Trial users see all tracks for 2 weeks
            SubscriptionStatus.UPLOAD_WITH_MONTH -> true // Upload tier with month bonus sees all tracks
            SubscriptionStatus.UPLOAD_EXPIRED -> {
                // Upload expired users get same access as free users
                (currentTime - releaseTimestamp) <= TimeUnit.DAYS.toMillis(FREE_VISIBILITY_DAYS)
            }
            SubscriptionStatus.FULL_PREMIUM -> true // Premium users see all tracks
        }
    }
    
    /**
     * Check if this track has expired for free users
     */
    fun isExpired(): Boolean {
        val currentTime = System.currentTimeMillis()
        return (currentTime - releaseTimestamp) > TimeUnit.DAYS.toMillis(FREE_VISIBILITY_DAYS)
    }
    
    /**
     * Get the expiration time for this track
     */
    fun getExpirationTime(): Long {
        return releaseTimestamp + TimeUnit.DAYS.toMillis(FREE_VISIBILITY_DAYS)
    }
    
    /**
     * Get display name for this track (Batch XXX Week YY Variant)
     */
    fun getDisplayName(): String {
        return "B${batch.toString().padStart(3, '0')} W$week ${variant.uppercase()}"
    }
    
    /**
     * Get sorting key for newest-first ordering (higher = newer)
     */
    fun getSortingKey(): Long {
        return year * 100000L + week * 100L + batch
    }
    
    /**
     * Get time remaining until expiration (in milliseconds)
     */
    fun getTimeUntilExpiration(): Long {
        val expirationTime = getExpirationTime()
        val currentTime = System.currentTimeMillis()
        return maxOf(0L, expirationTime - currentTime)
    }
    
    /**
     * Check if this track is yet to be released
     */
    fun isYetToBeReleased(): Boolean {
        return releaseTimestamp > System.currentTimeMillis()
    }
    
    /**
     * Get time until release (in milliseconds)
     */
    fun getTimeUntilRelease(): Long {
        val currentTime = System.currentTimeMillis()
        return maxOf(0L, releaseTimestamp - currentTime)
    }
    
    /**
     * Format time until release as human readable string
     */
    fun getTimeUntilReleaseFormatted(): String {
        val timeRemaining = getTimeUntilRelease()
        if (timeRemaining <= 0) return "Available now"
        
        val days = TimeUnit.MILLISECONDS.toDays(timeRemaining)
        val hours = TimeUnit.MILLISECONDS.toHours(timeRemaining) % 24
        val minutes = TimeUnit.MILLISECONDS.toMinutes(timeRemaining) % 60
        
        return when {
            days > 0 -> "${days}d ${hours}h"
            hours > 0 -> "${hours}h ${minutes}m"
            else -> "${minutes}m"
        }
    }
    
    /**
     * Format time remaining as human readable string
     */
    fun getTimeRemainingFormatted(): String {
        val timeRemaining = getTimeUntilExpiration()
        if (timeRemaining <= 0) return "Expired"
        
        val days = TimeUnit.MILLISECONDS.toDays(timeRemaining)
        val hours = TimeUnit.MILLISECONDS.toHours(timeRemaining) % 24
        val minutes = TimeUnit.MILLISECONDS.toMinutes(timeRemaining) % 60
        
        return when {
            days > 0 -> "${days}d ${hours}h"
            hours > 0 -> "${hours}h ${minutes}m"
            else -> "${minutes}m"
        }
    }
}

/**
 * User subscription status enum
 */
enum class SubscriptionStatus {
    FREE,                    // No trial, no payment - 14 day track access only
    TRIAL,                   // 2-week trial - full access  
    UPLOAD_WITH_MONTH,       // One-time purchase - 1 month full access + permanent file imports
    UPLOAD_EXPIRED,          // One-time purchase after 1st month - file imports only
    FULL_PREMIUM;            // Monthly subscription - everything ongoing
    
    fun canSaveTracks(): Boolean = when(this) {
        FREE -> false
        TRIAL -> false               // Trial = track access only, NO downloads
        UPLOAD_WITH_MONTH -> true
        UPLOAD_EXPIRED -> false      // Lost this after month expired
        FULL_PREMIUM -> true
    }
    
    fun canAccessBacklog(): Boolean = when(this) {
        FREE -> false
        TRIAL -> true
        UPLOAD_WITH_MONTH -> true
        UPLOAD_EXPIRED -> false      // Lost this after month expired
        FULL_PREMIUM -> true
    }
    
    fun hasEarlyAccess(): Boolean = when(this) {
        FREE -> false
        TRIAL -> true
        UPLOAD_WITH_MONTH -> true
        UPLOAD_EXPIRED -> false      // Lost this after month expired
        FULL_PREMIUM -> true
    }
    
    fun hasFullAccess(): Boolean = when(this) {
        FREE -> false
        TRIAL -> true
        UPLOAD_WITH_MONTH -> true
        UPLOAD_EXPIRED -> false      // Lost this after month expired
        FULL_PREMIUM -> true
    }
    
    fun canAccessNewTracks(): Boolean = this != FREE // All paid tiers get new tracks
    
    fun canImportCustomFiles(): Boolean = when(this) {
        FREE -> false
        TRIAL -> false                   // Trial only gets track access, no file imports
        UPLOAD_WITH_MONTH -> true        // Permanent file import ability
        UPLOAD_EXPIRED -> true           // Keep file imports permanently
        FULL_PREMIUM -> true
    }
    
    fun getDisplayName(): String = when(this) {
        FREE -> "AyoDJ Free"
        TRIAL -> "AyoDJ Trial"
        UPLOAD_WITH_MONTH -> "AyoDJ Upload+ (Premium)"
        UPLOAD_EXPIRED -> "AyoDJ Upload"
        FULL_PREMIUM -> "AyoDJ Premium"
    }
}

/**
 * Track visibility state for UI
 */
sealed class TrackState {
    object Available : TrackState()
    object Locked : TrackState()
    data class Expired(val upgradeMessage: String = "Upgrade for backlog access") : TrackState()
    data class YetToBeReleased(val releaseCountdown: String) : TrackState()
}
