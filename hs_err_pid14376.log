#
# A fatal error has been detected by the Java Runtime Environment:
#
#  EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffd113ec571, pid=14376, tid=16232
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# Problematic frame:
# V  [jvm.dll+0xc571]
#
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#
# If you would like to submit a bug report, please visit:
#   https://github.com/adoptium/adoptium-support/issues
#

---------------  S U M M A R Y ------------

Command Line: -Xmx64m -Xms64m -Dorg.gradle.appname=gradlew org.gradle.wrapper.GradleWrapperMain assembleDebug

Host: AMD Ryzen 5 2600X Six-Core Processor           , 12 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Fri Jul  4 16:37:55 2025 Pacific Daylight Time elapsed time: 0.368294 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x0000029277781ea0):  WorkerThread "GC Thread#1"    [id=16232, stack(0x000000c9d4400000,0x000000c9d4500000) (1024K)]

Stack: [0x000000c9d4400000,0x000000c9d4500000],  sp=0x000000c9d44ff3b8,  free space=1020k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0xc571]


siginfo: EXCEPTION_ACCESS_VIOLATION (0xc0000005), reading address 0x0000000000000100


Registers:
RAX=0x0000000000000000, RBX=0x0000000000000001, RCX=0x000002920f000000, RDX=0x00000000ffd615c8
RSP=0x000000c9d44ff3b8, RBP=0x000000000000019f, RSI=0x000002926bafae00, RDI=0x000000b60104c5c8
R8 =0x000002920f000000, R9 =0x0000000000000000, R10=0x0000000000000010, R11=0x0000000000000001
R12=0x00000292774a0bc8, R13=0x00000000ffd615c8, R14=0x000002927761f140, R15=0x000002920f000000
RIP=0x00007ffd113ec571, EFLAGS=0x0000000000010246

XMM[0]=0x0202020202020202 0x0202020202020202
XMM[1]=0xffd65740ffd65688 0xffd655e8ffd65548
XMM[2]=0xffd64260ffd641b8 0xffd64110ffd64028
XMM[3]=0xffd648e0ffd647f8 0xffd64710ffd64628
XMM[4]=0xffd65080ffd64fe0 0xffd64f30ffd64e00
XMM[5]=0xffd65c10ffd65b68 0xffd65a28ffd65970
XMM[6]=0x0000000000000000 0x0000000000000000
XMM[7]=0x0000000000000000 0x0000000000000000
XMM[8]=0x0000000000000000 0x0000000000000000
XMM[9]=0x0000000000000000 0x0000000000000000
XMM[10]=0x0000000000000000 0x0000000000000000
XMM[11]=0x0000000000000000 0x0000000000000000
XMM[12]=0x0000000000000000 0x0000000000000000
XMM[13]=0x0000000000000000 0x0000000000000000
XMM[14]=0x0000000000000000 0x0000000000000000
XMM[15]=0x0000000000000000 0x0000000000000000
  MXCSR=0x00001fa0


Register to memory mapping:

RAX=0x0 is null
RBX=0x0000000000000001 is an unknown value
RCX=0x000002920f000000 is pointing into metadata
RDX=
[error occurred during error reporting (printing register info), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffd113ec506]
RSP=0x000000c9d44ff3b8 points into unknown readable memory: 0x00007ffd11737f3b | 3b 7f 73 11 fd 7f 00 00
RBP=0x000000000000019f is an unknown value
RSI=0x000002926bafae00 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
RDI=0x000000b60104c5c8 is an unknown value
R8 =0x000002920f000000 is pointing into metadata
R9 =0x0 is null
R10=0x0000000000000010 is an unknown value
R11=0x0000000000000001 is an unknown value
R12=0x00000292774a0bc8 points into unknown readable memory: 0x88000a00db5f9c93 | 93 9c 5f db 00 0a 00 88
R13=
[error occurred during error reporting (printing register info, attempt 2), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffd113ec506]
R14=0x000002927761f140 points into unknown readable memory: 0x00007ffd11e0bca8 | a8 bc e0 11 fd 7f 00 00
R15=0x000002920f000000 is pointing into metadata

Top of Stack: (sp=0x000000c9d44ff3b8)
0x000000c9d44ff3b8:   00007ffd11737f3b 0000000000000001
0x000000c9d44ff3c8:   0000000000000001 0000000000000001
0x000000c9d44ff3d8:   0000000000000000 00000000fe8f88ec
0x000000c9d44ff3e8:   00007ffd11736308 0000000000000001
0x000000c9d44ff3f8:   000002926bb42140 000000c900000000
0x000000c9d44ff408:   000000c9d44ff468 000002927761f2c8
0x000000c9d44ff418:   0000000000000000 0000000000000040
0x000000c9d44ff428:   00000000000003d8 00000292774a0bc8
0x000000c9d44ff438:   000002927761f140 000002926bafae00
0x000000c9d44ff448:   000000000000019f 00000000fe7031e4
0x000000c9d44ff458:   00007ffd11739163 00000000ffd60000
0x000000c9d44ff468:   0000000000000001 0000029200000000
0x000000c9d44ff478:   0000000000000028 000002927c283b20
0x000000c9d44ff488:   000002927782cae0 000002927758d680
0x000000c9d44ff498:   000002927782cb18 0000000000000014
0x000000c9d44ff4a8:   00007ffd117358ce 000002927787b018
0x000000c9d44ff4b8:   000002927761f140 000002927782cae0
0x000000c9d44ff4c8:   00000000000003d8 0000000000374c30
0x000000c9d44ff4d8:   0000000000000000 000002927782cb18
0x000000c9d44ff4e8:   0000000000000014 000002927782cb18
0x000000c9d44ff4f8:   00007ffd11745e34 000000c9d44f0000
0x000000c9d44ff508:   0000029277820001 0000029200000000
0x000000c9d44ff518:   0000000000000070 000002927758d620
0x000000c9d44ff528:   00007ffd11601f0d 000002927758d680
0x000000c9d44ff538:   000002926baffa30 000002927782ca01
0x000000c9d44ff548:   00007ffd11c3d207 000002927782cb78
0x000000c9d44ff558:   00007ffd1173584b 000002927782cb78
0x000000c9d44ff568:   000002927782cb78 0000000000000001
0x000000c9d44ff578:   00007ffd11728802 000002926bb77de0
0x000000c9d44ff588:   00007ffd11604118 0000000000372afa
0x000000c9d44ff598:   0000000000000000 0000000000372afd
0x000000c9d44ff5a8:   0000000000000000 000002927782ca01 

Instructions: (pc=0x00007ffd113ec571)
0x00007ffd113ec471:   41 f6 c1 01 75 53 41 c1 f9 03 49 63 c1 c3 79 49
0x00007ffd113ec481:   84 c0 41 b8 0c 00 00 00 8b 05 99 dc c1 00 b9 10
0x00007ffd113ec491:   00 00 00 41 0f 45 c8 ff c8 4c 63 c0 41 8b c1 48
0x00007ffd113ec4a1:   c1 f8 10 0f b6 c0 48 63 14 11 41 8b c9 83 e1 3f
0x00007ffd113ec4b1:   48 d3 e2 49 8d 0c 10 49 f7 d0 48 03 c1 49 c1 e8
0x00007ffd113ec4c1:   03 48 c1 e8 03 49 23 c0 c3 49 8b 00 49 8b c8 48
0x00007ffd113ec4d1:   ff a0 00 01 00 00 cc cc cc cc cc cc cc cc cc 8b
0x00007ffd113ec4e1:   44 24 30 45 03 c9 44 2b 4c 24 28 83 c0 0b 44 03
0x00007ffd113ec4f1:   ca 45 03 c8 41 03 c1 c3 cc cc cc cc cc cc cc 4c
0x00007ffd113ec501:   8b c2 48 8b d1 45 8b 48 08 45 85 c9 7e 0e 41 f6
0x00007ffd113ec511:   c1 01 75 56 41 c1 f9 03 49 63 c1 c3 79 4c 80 3d
0x00007ffd113ec521:   fc e0 c1 00 00 b8 0c 00 00 00 b9 10 00 00 00 0f
0x00007ffd113ec531:   45 c8 8b 05 ef db c1 00 ff c8 4c 63 c0 41 8b c1
0x00007ffd113ec541:   48 c1 f8 10 48 63 14 11 41 8b c9 83 e1 3f 0f b6
0x00007ffd113ec551:   c0 48 d3 e2 49 8d 0c 10 49 f7 d0 48 03 c1 49 c1
0x00007ffd113ec561:   e8 03 48 c1 e8 03 49 23 c0 c3 49 8b 00 49 8b c8
0x00007ffd113ec571:   48 ff a0 00 01 00 00 cc cc cc cc cc cc cc cc 48
0x00007ffd113ec581:   8b 41 08 0f b7 48 2c 0f b7 40 2a 83 c0 48 03 c1
0x00007ffd113ec591:   c3 cc cc cc cc cc cc cc cc cc cc cc cc cc cc 48
0x00007ffd113ec5a1:   89 5c 24 08 57 48 83 ec 20 0f b6 05 8f dd cc 00
0x00007ffd113ec5b1:   48 8b fa 48 8b d9 84 c0 74 4f 48 85 15 26 d5 cc
0x00007ffd113ec5c1:   00 74 38 48 8b ca e8 54 d5 8c 00 48 8b d0 48 85
0x00007ffd113ec5d1:   db 74 25 48 85 c0 74 20 48 8b c7 f0 48 0f b1 13
0x00007ffd113ec5e1:   74 16 48 85 05 fe d4 cc 00 74 0d 48 8b c8 f0 48
0x00007ffd113ec5f1:   0f b1 13 48 3b c1 75 ea 48 8b fa 48 8b c7 48 8b
0x00007ffd113ec601:   5c 24 30 48 83 c4 20 5f c3 48 85 3d df d4 cc 00
0x00007ffd113ec611:   75 33 48 85 ff 75 0f 33 d2 8b c2 48 8b 5c 24 30
0x00007ffd113ec621:   48 83 c4 20 5f c3 48 8b 15 d2 d4 cc 00 48 23 d7
0x00007ffd113ec631:   48 0b 15 a8 d4 cc 00 48 8b c2 48 8b 5c 24 30 48
0x00007ffd113ec641:   83 c4 20 5f c3 48 8b cf e8 12 d4 8c 00 48 8b d0
0x00007ffd113ec651:   48 85 db 74 40 48 85 c0 74 3b 4c 8b 05 9e d4 cc
0x00007ffd113ec661:   00 4c 23 c0 4c 0b 05 cc d4 cc 00 74 28 48 8b c7 


Stack slot to memory mapping:

stack at sp + 0 slots: 0x00007ffd11737f3b jvm.dll
stack at sp + 1 slots: 0x0000000000000001 is an unknown value
stack at sp + 2 slots: 0x0000000000000001 is an unknown value
stack at sp + 3 slots: 0x0000000000000001 is an unknown value
stack at sp + 4 slots: 0x0 is null
stack at sp + 5 slots: 
[error occurred during error reporting (inspecting top of stack), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffd113ec506]
stack at sp + 6 slots: 0x00007ffd11736308 jvm.dll
stack at sp + 7 slots: 0x0000000000000001 is an unknown value


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000292777d7e90, length=13, elements={
0x00000292697945b0, 0x00000292773f2840, 0x00000292773f3150, 0x00000292773f3f50,
0x00000292773f93d0, 0x00000292773fa250, 0x00000292773fb000, 0x0000029277409860,
0x000002927740c0c0, 0x0000029277587bb0, 0x0000029277593090, 0x00000292777829e0,
0x000002927782fea0
}

Java Threads: ( => current thread )
  0x00000292697945b0 JavaThread "main"                              [_thread_blocked, id=5240, stack(0x000000c9d3000000,0x000000c9d3100000) (1024K)]
  0x00000292773f2840 JavaThread "Reference Handler"          daemon [_thread_blocked, id=16064, stack(0x000000c9d3800000,0x000000c9d3900000) (1024K)]
  0x00000292773f3150 JavaThread "Finalizer"                  daemon [_thread_blocked, id=9396, stack(0x000000c9d3900000,0x000000c9d3a00000) (1024K)]
  0x00000292773f3f50 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=868, stack(0x000000c9d3a00000,0x000000c9d3b00000) (1024K)]
  0x00000292773f93d0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=10556, stack(0x000000c9d3b00000,0x000000c9d3c00000) (1024K)]
  0x00000292773fa250 JavaThread "Service Thread"             daemon [_thread_blocked, id=5932, stack(0x000000c9d3c00000,0x000000c9d3d00000) (1024K)]
  0x00000292773fb000 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=16456, stack(0x000000c9d3d00000,0x000000c9d3e00000) (1024K)]
  0x0000029277409860 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=18352, stack(0x000000c9d3e00000,0x000000c9d3f00000) (1024K)]
  0x000002927740c0c0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=16924, stack(0x000000c9d3f00000,0x000000c9d4000000) (1024K)]
  0x0000029277587bb0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=11644, stack(0x000000c9d4000000,0x000000c9d4100000) (1024K)]
  0x0000029277593090 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=5988, stack(0x000000c9d4100000,0x000000c9d4200000) (1024K)]
  0x00000292777829e0 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=9412, stack(0x000000c9d4200000,0x000000c9d4300000) (1024K)]
  0x000002927782fea0 JavaThread "C2 CompilerThread2"         daemon [_thread_blocked, id=10304, stack(0x000000c9d4300000,0x000000c9d4400000) (1024K)]
Total: 13

Other Threads:
  0x000002926bb995a0 VMThread "VM Thread"                           [id=5484, stack(0x000000c9d3700000,0x000000c9d3800000) (1024K)]
  0x00000292773ea0c0 WatcherThread "VM Periodic Task Thread"        [id=14868, stack(0x000000c9d3600000,0x000000c9d3700000) (1024K)]
  0x000002926baffa30 WorkerThread "GC Thread#0"                     [id=17124, stack(0x000000c9d3100000,0x000000c9d3200000) (1024K)]
=>0x0000029277781ea0 WorkerThread "GC Thread#1"                     [id=16232, stack(0x000000c9d4400000,0x000000c9d4500000) (1024K)]
  0x000002926bb01120 ConcurrentGCThread "G1 Main Marker"            [id=13208, stack(0x000000c9d3200000,0x000000c9d3300000) (1024K)]
  0x000002926bb01ce0 WorkerThread "G1 Conc#0"                       [id=8840, stack(0x000000c9d3300000,0x000000c9d3400000) (1024K)]
  0x000002926bb7b830 ConcurrentGCThread "G1 Refine#0"               [id=18284, stack(0x000000c9d3400000,0x000000c9d3500000) (1024K)]
  0x000002926bb7c2b0 ConcurrentGCThread "G1 Service"                [id=3616, stack(0x000000c9d3500000,0x000000c9d3600000) (1024K)]
Total: 8

Threads with active compile tasks:
C2 CompilerThread0  381  559       4       java.util.HashSet::add (20 bytes)
C1 CompilerThread0  381  656       1       sun.nio.fs.WindowsPathParser$Result::root (5 bytes)
C2 CompilerThread1  381  644       4       java.lang.AbstractStringBuilder::append (77 bytes)
Total: 3

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffd1208e308] Threads_lock - owner thread: 0x000002926bb995a0
[0x00007ffd1208e408] Heap_lock - owner thread: 0x00000292697945b0

Heap address: 0x00000000fc000000, size: 64 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000002920f000000-0x000002920fc80000-0x000002920fc80000), size 13107200, SharedBaseAddress: 0x000002920f000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000029210000000-0x0000029250000000, reserved size: 1073741824
Narrow klass base: 0x000002920f000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 12 size 40 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 12 total, 12 available
 Memory: 32716M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 64M
 Heap Initial Capacity: 64M
 Heap Max Capacity: 64M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 65536K, used 23552K [0x00000000fc000000, 0x0000000100000000)
  region size 1024K, 25 young (25600K), 2 survivors (2048K)
 Metaspace       used 1704K, committed 1920K, reserved 1114112K
  class space    used 152K, committed 256K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x00000000fc000000, 0x00000000fc000000, 0x00000000fc100000|  0%| F|  |TAMS 0x00000000fc000000| PB 0x00000000fc000000| Untracked 
|   1|0x00000000fc100000, 0x00000000fc100000, 0x00000000fc200000|  0%| F|  |TAMS 0x00000000fc100000| PB 0x00000000fc100000| Untracked 
|   2|0x00000000fc200000, 0x00000000fc200000, 0x00000000fc300000|  0%| F|  |TAMS 0x00000000fc200000| PB 0x00000000fc200000| Untracked 
|   3|0x00000000fc300000, 0x00000000fc300000, 0x00000000fc400000|  0%| F|  |TAMS 0x00000000fc300000| PB 0x00000000fc300000| Untracked 
|   4|0x00000000fc400000, 0x00000000fc400000, 0x00000000fc500000|  0%| F|  |TAMS 0x00000000fc400000| PB 0x00000000fc400000| Untracked 
|   5|0x00000000fc500000, 0x00000000fc500000, 0x00000000fc600000|  0%| F|  |TAMS 0x00000000fc500000| PB 0x00000000fc500000| Untracked 
|   6|0x00000000fc600000, 0x00000000fc600000, 0x00000000fc700000|  0%| F|  |TAMS 0x00000000fc600000| PB 0x00000000fc600000| Untracked 
|   7|0x00000000fc700000, 0x00000000fc700000, 0x00000000fc800000|  0%| F|  |TAMS 0x00000000fc700000| PB 0x00000000fc700000| Untracked 
|   8|0x00000000fc800000, 0x00000000fc800000, 0x00000000fc900000|  0%| F|  |TAMS 0x00000000fc800000| PB 0x00000000fc800000| Untracked 
|   9|0x00000000fc900000, 0x00000000fc900000, 0x00000000fca00000|  0%| F|  |TAMS 0x00000000fc900000| PB 0x00000000fc900000| Untracked 
|  10|0x00000000fca00000, 0x00000000fca00000, 0x00000000fcb00000|  0%| F|  |TAMS 0x00000000fca00000| PB 0x00000000fca00000| Untracked 
|  11|0x00000000fcb00000, 0x00000000fcb00000, 0x00000000fcc00000|  0%| F|  |TAMS 0x00000000fcb00000| PB 0x00000000fcb00000| Untracked 
|  12|0x00000000fcc00000, 0x00000000fcc00000, 0x00000000fcd00000|  0%| F|  |TAMS 0x00000000fcc00000| PB 0x00000000fcc00000| Untracked 
|  13|0x00000000fcd00000, 0x00000000fcd00000, 0x00000000fce00000|  0%| F|  |TAMS 0x00000000fcd00000| PB 0x00000000fcd00000| Untracked 
|  14|0x00000000fce00000, 0x00000000fce00000, 0x00000000fcf00000|  0%| F|  |TAMS 0x00000000fce00000| PB 0x00000000fce00000| Untracked 
|  15|0x00000000fcf00000, 0x00000000fcf00000, 0x00000000fd000000|  0%| F|  |TAMS 0x00000000fcf00000| PB 0x00000000fcf00000| Untracked 
|  16|0x00000000fd000000, 0x00000000fd000000, 0x00000000fd100000|  0%| F|  |TAMS 0x00000000fd000000| PB 0x00000000fd000000| Untracked 
|  17|0x00000000fd100000, 0x00000000fd100000, 0x00000000fd200000|  0%| F|  |TAMS 0x00000000fd100000| PB 0x00000000fd100000| Untracked 
|  18|0x00000000fd200000, 0x00000000fd200000, 0x00000000fd300000|  0%| F|  |TAMS 0x00000000fd200000| PB 0x00000000fd200000| Untracked 
|  19|0x00000000fd300000, 0x00000000fd300000, 0x00000000fd400000|  0%| F|  |TAMS 0x00000000fd300000| PB 0x00000000fd300000| Untracked 
|  20|0x00000000fd400000, 0x00000000fd400000, 0x00000000fd500000|  0%| F|  |TAMS 0x00000000fd400000| PB 0x00000000fd400000| Untracked 
|  21|0x00000000fd500000, 0x00000000fd500000, 0x00000000fd600000|  0%| F|  |TAMS 0x00000000fd500000| PB 0x00000000fd500000| Untracked 
|  22|0x00000000fd600000, 0x00000000fd600000, 0x00000000fd700000|  0%| F|  |TAMS 0x00000000fd600000| PB 0x00000000fd600000| Untracked 
|  23|0x00000000fd700000, 0x00000000fd700000, 0x00000000fd800000|  0%| F|  |TAMS 0x00000000fd700000| PB 0x00000000fd700000| Untracked 
|  24|0x00000000fd800000, 0x00000000fd800000, 0x00000000fd900000|  0%| F|  |TAMS 0x00000000fd800000| PB 0x00000000fd800000| Untracked 
|  25|0x00000000fd900000, 0x00000000fd900000, 0x00000000fda00000|  0%| F|  |TAMS 0x00000000fd900000| PB 0x00000000fd900000| Untracked 
|  26|0x00000000fda00000, 0x00000000fda00000, 0x00000000fdb00000|  0%| F|  |TAMS 0x00000000fda00000| PB 0x00000000fda00000| Untracked 
|  27|0x00000000fdb00000, 0x00000000fdb00000, 0x00000000fdc00000|  0%| F|  |TAMS 0x00000000fdb00000| PB 0x00000000fdb00000| Untracked 
|  28|0x00000000fdc00000, 0x00000000fdc00000, 0x00000000fdd00000|  0%| F|  |TAMS 0x00000000fdc00000| PB 0x00000000fdc00000| Untracked 
|  29|0x00000000fdd00000, 0x00000000fdd00000, 0x00000000fde00000|  0%| F|  |TAMS 0x00000000fdd00000| PB 0x00000000fdd00000| Untracked 
|  30|0x00000000fde00000, 0x00000000fde00000, 0x00000000fdf00000|  0%| F|  |TAMS 0x00000000fde00000| PB 0x00000000fde00000| Untracked 
|  31|0x00000000fdf00000, 0x00000000fdf00000, 0x00000000fe000000|  0%| F|  |TAMS 0x00000000fdf00000| PB 0x00000000fdf00000| Untracked 
|  32|0x00000000fe000000, 0x00000000fe000000, 0x00000000fe100000|  0%| F|  |TAMS 0x00000000fe000000| PB 0x00000000fe000000| Untracked 
|  33|0x00000000fe100000, 0x00000000fe100000, 0x00000000fe200000|  0%| F|  |TAMS 0x00000000fe100000| PB 0x00000000fe100000| Untracked 
|  34|0x00000000fe200000, 0x00000000fe200000, 0x00000000fe300000|  0%| F|  |TAMS 0x00000000fe200000| PB 0x00000000fe200000| Untracked 
|  35|0x00000000fe300000, 0x00000000fe300000, 0x00000000fe400000|  0%| F|  |TAMS 0x00000000fe300000| PB 0x00000000fe300000| Untracked 
|  36|0x00000000fe400000, 0x00000000fe400000, 0x00000000fe500000|  0%| F|  |TAMS 0x00000000fe400000| PB 0x00000000fe400000| Untracked 
|  37|0x00000000fe500000, 0x00000000fe500000, 0x00000000fe600000|  0%| F|  |TAMS 0x00000000fe500000| PB 0x00000000fe500000| Untracked 
|  38|0x00000000fe600000, 0x00000000fe600000, 0x00000000fe700000|  0%| F|  |TAMS 0x00000000fe600000| PB 0x00000000fe600000| Untracked 
|  39|0x00000000fe700000, 0x00000000fe7c8000, 0x00000000fe800000| 78%| S|  |TAMS 0x00000000fe700000| PB 0x00000000fe700000| Complete 
|  40|0x00000000fe800000, 0x00000000fe900000, 0x00000000fe900000|100%| S|  |TAMS 0x00000000fe800000| PB 0x00000000fe800000| Complete 
|  41|0x00000000fe900000, 0x00000000fea00000, 0x00000000fea00000|100%| E|CS|TAMS 0x00000000fe900000| PB 0x00000000fe900000| Complete 
|  42|0x00000000fea00000, 0x00000000feb00000, 0x00000000feb00000|100%| E|CS|TAMS 0x00000000fea00000| PB 0x00000000fea00000| Complete 
|  43|0x00000000feb00000, 0x00000000fec00000, 0x00000000fec00000|100%| E|CS|TAMS 0x00000000feb00000| PB 0x00000000feb00000| Complete 
|  44|0x00000000fec00000, 0x00000000fed00000, 0x00000000fed00000|100%| E|CS|TAMS 0x00000000fec00000| PB 0x00000000fec00000| Complete 
|  45|0x00000000fed00000, 0x00000000fee00000, 0x00000000fee00000|100%| E|CS|TAMS 0x00000000fed00000| PB 0x00000000fed00000| Complete 
|  46|0x00000000fee00000, 0x00000000fef00000, 0x00000000fef00000|100%| E|CS|TAMS 0x00000000fee00000| PB 0x00000000fee00000| Complete 
|  47|0x00000000fef00000, 0x00000000ff000000, 0x00000000ff000000|100%| E|CS|TAMS 0x00000000fef00000| PB 0x00000000fef00000| Complete 
|  48|0x00000000ff000000, 0x00000000ff100000, 0x00000000ff100000|100%| E|CS|TAMS 0x00000000ff000000| PB 0x00000000ff000000| Complete 
|  49|0x00000000ff100000, 0x00000000ff200000, 0x00000000ff200000|100%| E|CS|TAMS 0x00000000ff100000| PB 0x00000000ff100000| Complete 
|  50|0x00000000ff200000, 0x00000000ff300000, 0x00000000ff300000|100%| E|CS|TAMS 0x00000000ff200000| PB 0x00000000ff200000| Complete 
|  51|0x00000000ff300000, 0x00000000ff400000, 0x00000000ff400000|100%| E|CS|TAMS 0x00000000ff300000| PB 0x00000000ff300000| Complete 
|  52|0x00000000ff400000, 0x00000000ff500000, 0x00000000ff500000|100%| E|CS|TAMS 0x00000000ff400000| PB 0x00000000ff400000| Complete 
|  53|0x00000000ff500000, 0x00000000ff600000, 0x00000000ff600000|100%| E|CS|TAMS 0x00000000ff500000| PB 0x00000000ff500000| Complete 
|  54|0x00000000ff600000, 0x00000000ff700000, 0x00000000ff700000|100%| E|CS|TAMS 0x00000000ff600000| PB 0x00000000ff600000| Complete 
|  55|0x00000000ff700000, 0x00000000ff800000, 0x00000000ff800000|100%| E|CS|TAMS 0x00000000ff700000| PB 0x00000000ff700000| Complete 
|  56|0x00000000ff800000, 0x00000000ff900000, 0x00000000ff900000|100%| E|CS|TAMS 0x00000000ff800000| PB 0x00000000ff800000| Complete 
|  57|0x00000000ff900000, 0x00000000ffa00000, 0x00000000ffa00000|100%| E|CS|TAMS 0x00000000ff900000| PB 0x00000000ff900000| Complete 
|  58|0x00000000ffa00000, 0x00000000ffb00000, 0x00000000ffb00000|100%| E|CS|TAMS 0x00000000ffa00000| PB 0x00000000ffa00000| Complete 
|  59|0x00000000ffb00000, 0x00000000ffc00000, 0x00000000ffc00000|100%| E|CS|TAMS 0x00000000ffb00000| PB 0x00000000ffb00000| Complete 
|  60|0x00000000ffc00000, 0x00000000ffd00000, 0x00000000ffd00000|100%| E|CS|TAMS 0x00000000ffc00000| PB 0x00000000ffc00000| Complete 
|  61|0x00000000ffd00000, 0x00000000ffe00000, 0x00000000ffe00000|100%| E|CS|TAMS 0x00000000ffd00000| PB 0x00000000ffd00000| Complete 
|  62|0x00000000ffe00000, 0x00000000fff00000, 0x00000000fff00000|100%| E|CS|TAMS 0x00000000ffe00000| PB 0x00000000ffe00000| Complete 
|  63|0x00000000fff00000, 0x0000000100000000, 0x0000000100000000|100%| E|CS|TAMS 0x00000000fff00000| PB 0x00000000fff00000| Complete 

Card table byte_map: [0x000002926ba10000,0x000002926ba30000] _byte_map_base: 0x000002926b230000

Marking Bits: (CMBitMap*) 0x000002926bb00040
 Bits: [0x0000029274730000, 0x0000029274830000)

Polling page: 0x0000029269950000

Metaspace:

Usage:
  Non-class:      1.52 MB used.
      Class:    152.73 KB used.
       Both:      1.66 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       1.62 MB (  3%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     256.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       1.88 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  11.64 MB
       Class:  15.69 MB
        Both:  27.32 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 26.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 30.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 58.
num_chunk_merges: 0.
num_chunk_splits: 33.
num_chunks_enlarged: 22.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=259Kb max_used=259Kb free=119740Kb
 bounds [0x0000029207ad0000, 0x0000029207d40000, 0x000002920f000000]
CodeHeap 'profiled nmethods': size=120000Kb used=1065Kb max_used=1065Kb free=118934Kb
 bounds [0x0000029200000000, 0x0000029200270000, 0x0000029207530000]
CodeHeap 'non-nmethods': size=5760Kb used=1309Kb max_used=1338Kb free=4450Kb
 bounds [0x0000029207530000, 0x00000292077a0000, 0x0000029207ad0000]
 total_blobs=1075 nmethods=653 adapters=327
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.359 Thread 0x000002927740c0c0  651       3       java.util.Objects::checkFromIndexSize (8 bytes)
Event: 0.359 Thread 0x000002927740c0c0 nmethod 651 0x0000029200108d10 code [0x0000029200108ec0, 0x00000292001090c8]
Event: 0.359 Thread 0x000002927740c0c0  647   !   3       java.util.zip.ZipFile::getEntry (59 bytes)
Event: 0.359 Thread 0x000002927740c0c0 nmethod 647 0x0000029200109210 code [0x0000029200109400, 0x0000029200109880]
Event: 0.359 Thread 0x000002927740c0c0  648       3       java.util.ArrayDeque::add (7 bytes)
Event: 0.359 Thread 0x000002927740c0c0 nmethod 648 0x0000029200109b10 code [0x0000029200109cc0, 0x0000029200109e18]
Event: 0.359 Thread 0x000002927740c0c0  649       3       java.util.ArrayDeque::addLast (51 bytes)
Event: 0.359 Thread 0x000002927740c0c0 nmethod 649 0x0000029200109f10 code [0x000002920010a100, 0x000002920010a5b0]
Event: 0.360 Thread 0x000002927782fea0 nmethod 643 0x0000029207b0e810 code [0x0000029207b0e9e0, 0x0000029207b0f038]
Event: 0.360 Thread 0x000002927782fea0  650       4       java.lang.Math::max (11 bytes)
Event: 0.360 Thread 0x000002927782fea0 nmethod 650 0x0000029207b0f310 code [0x0000029207b0f480, 0x0000029207b0f4f0]
Event: 0.360 Thread 0x000002927782fea0  653       4       jdk.internal.util.Preconditions::checkFromIndexSize (25 bytes)
Event: 0.360 Thread 0x00000292777829e0 nmethod 630% 0x0000029207b0f610 code [0x0000029207b0f800, 0x0000029207b0ff70]
Event: 0.361 Thread 0x00000292777829e0  644       4       java.lang.AbstractStringBuilder::append (77 bytes)
Event: 0.361 Thread 0x000002927782fea0 nmethod 653 0x0000029207b10390 code [0x0000029207b10520, 0x0000029207b105f0]
Event: 0.361 Thread 0x000002927782fea0  654       4       java.io.WinNTFileSystem::isSlash (18 bytes)
Event: 0.361 Thread 0x000002927740c0c0  655       1       sun.nio.fs.WindowsPathParser$Result::type (5 bytes)
Event: 0.361 Thread 0x000002927740c0c0 nmethod 655 0x0000029207b10690 code [0x0000029207b10820, 0x0000029207b108e8]
Event: 0.361 Thread 0x000002927740c0c0  656       1       sun.nio.fs.WindowsPathParser$Result::root (5 bytes)
Event: 0.361 Thread 0x000002927782fea0 nmethod 654 0x0000029207b10990 code [0x0000029207b10b20, 0x0000029207b10bc0]

GC Heap History (1 events):
Event: 0.361 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 65536K, used 23552K [0x00000000fc000000, 0x0000000100000000)
  region size 1024K, 23 young (23552K), 0 survivors (0K)
 Metaspace       used 1704K, committed 1920K, reserved 1114112K
  class space    used 152K, committed 256K, reserved 1048576K
}

Dll operation events (6 events):
Event: 0.005 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\java.dll
Event: 0.032 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\jsvml.dll
Event: 0.050 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\net.dll
Event: 0.051 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\nio.dll
Event: 0.054 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\zip.dll
Event: 0.331 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\jimage.dll

Deoptimization events (20 events):
Event: 0.315 Thread 0x00000292697945b0 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000029207b02e90 relative=0x0000000000000710
Event: 0.315 Thread 0x00000292697945b0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000029207b02e90 method=java.util.HashMap.putVal(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; @ 253 c2
Event: 0.315 Thread 0x00000292697945b0 DEOPT PACKING pc=0x0000029207b02e90 sp=0x000000c9d30feac0
Event: 0.315 Thread 0x00000292697945b0 DEOPT UNPACKING pc=0x00000292075846a2 sp=0x000000c9d30fea50 mode 2
Event: 0.322 Thread 0x00000292697945b0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000029207b04728 relative=0x0000000000000788
Event: 0.322 Thread 0x00000292697945b0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000029207b04728 method=java.util.HashMap.putVal(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; @ 81 c2
Event: 0.322 Thread 0x00000292697945b0 DEOPT PACKING pc=0x0000029207b04728 sp=0x000000c9d30fee80
Event: 0.322 Thread 0x00000292697945b0 DEOPT UNPACKING pc=0x00000292075846a2 sp=0x000000c9d30fed90 mode 2
Event: 0.351 Thread 0x00000292697945b0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000029207b03764 relative=0x0000000000000224
Event: 0.351 Thread 0x00000292697945b0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000029207b03764 method=java.io.WinNTFileSystem.normalize(Ljava/lang/String;)Ljava/lang/String; @ 104 c2
Event: 0.351 Thread 0x00000292697945b0 DEOPT PACKING pc=0x0000029207b03764 sp=0x000000c9d30fe4b0
Event: 0.351 Thread 0x00000292697945b0 DEOPT UNPACKING pc=0x00000292075846a2 sp=0x000000c9d30fe438 mode 2
Event: 0.352 Thread 0x00000292697945b0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000029207af8e34 relative=0x0000000000000814
Event: 0.352 Thread 0x00000292697945b0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000029207af8e34 method=java.util.zip.ZipFile$Source.checkAndAddEntry(II)I @ 50 c2
Event: 0.352 Thread 0x00000292697945b0 DEOPT PACKING pc=0x0000029207af8e34 sp=0x000000c9d30fe150
Event: 0.352 Thread 0x00000292697945b0 DEOPT UNPACKING pc=0x00000292075846a2 sp=0x000000c9d30fe0c0 mode 2
Event: 0.360 Thread 0x00000292697945b0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000029207af9b3c relative=0x000000000000017c
Event: 0.360 Thread 0x00000292697945b0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000029207af9b3c method=java.util.zip.ZipFile$Source.isMetaName([BII)Z @ 43 c2
Event: 0.361 Thread 0x00000292697945b0 DEOPT PACKING pc=0x0000029207af9b3c sp=0x000000c9d30fc910
Event: 0.361 Thread 0x00000292697945b0 DEOPT UNPACKING pc=0x00000292075846a2 sp=0x000000c9d30fc8b8 mode 2

Classes loaded (20 events):
Event: 0.129 Loading class java/io/FilePermission$1
Event: 0.129 Loading class jdk/internal/access/JavaIOFilePermissionAccess
Event: 0.129 Loading class jdk/internal/access/JavaIOFilePermissionAccess done
Event: 0.129 Loading class java/io/FilePermission$1 done
Event: 0.130 Loading class java/io/FilePermissionCollection
Event: 0.130 Loading class java/io/FilePermissionCollection done
Event: 0.130 Loading class java/util/Vector
Event: 0.130 Loading class java/util/Vector done
Event: 0.142 Loading class java/net/MalformedURLException
Event: 0.142 Loading class java/net/MalformedURLException done
Event: 0.143 Loading class java/io/FileFilter
Event: 0.143 Loading class java/io/FileFilter done
Event: 0.158 Loading class java/lang/IndexOutOfBoundsException
Event: 0.158 Loading class java/lang/IndexOutOfBoundsException done
Event: 0.160 Loading class java/util/AbstractList$Itr
Event: 0.160 Loading class java/util/AbstractList$Itr done
Event: 0.161 Loading class java/util/zip/ZipException
Event: 0.161 Loading class java/util/zip/ZipException done
Event: 0.172 Loading class java/lang/invoke/DirectMethodHandle$Special
Event: 0.172 Loading class java/lang/invoke/DirectMethodHandle$Special done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (3 events):
Event: 0.131 Thread 0x00000292697945b0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ffc57eb0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x00000000ffc57eb0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.131 Thread 0x00000292697945b0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ffc5bb60}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000ffc5bb60) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.140 Thread 0x00000292697945b0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ffcaef48}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000000ffcaef48) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]

ZGC Phase Switch (0 events):
No events

VM Operations (3 events):
Event: 0.055 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.055 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.361 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (13 events):
Event: 0.018 Thread 0x00000292697945b0 Thread added: 0x00000292697945b0
Event: 0.031 Thread 0x00000292697945b0 Thread added: 0x00000292773f2840
Event: 0.031 Thread 0x00000292697945b0 Thread added: 0x00000292773f3150
Event: 0.031 Thread 0x00000292697945b0 Thread added: 0x00000292773f3f50
Event: 0.031 Thread 0x00000292697945b0 Thread added: 0x00000292773f93d0
Event: 0.031 Thread 0x00000292697945b0 Thread added: 0x00000292773fa250
Event: 0.031 Thread 0x00000292697945b0 Thread added: 0x00000292773fb000
Event: 0.031 Thread 0x00000292697945b0 Thread added: 0x0000029277409860
Event: 0.031 Thread 0x00000292697945b0 Thread added: 0x000002927740c0c0
Event: 0.045 Thread 0x00000292697945b0 Thread added: 0x0000029277587bb0
Event: 0.047 Thread 0x00000292697945b0 Thread added: 0x0000029277593090
Event: 0.321 Thread 0x000002927740c0c0 Thread added: 0x00000292777829e0
Event: 0.322 Thread 0x0000029277409860 Thread added: 0x000002927782fea0


Dynamic libraries:
0x00007ff65d620000 - 0x00007ff65d62e000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\java.exe
0x00007ffd8f5b0000 - 0x00007ffd8f7a8000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffd8d790000 - 0x00007ffd8d852000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffd8cf70000 - 0x00007ffd8d266000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffd8cda0000 - 0x00007ffd8cea0000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffd63b90000 - 0x00007ffd63ba8000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\jli.dll
0x00007ffd63a40000 - 0x00007ffd63a5e000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\VCRUNTIME140.dll
0x00007ffd8eb50000 - 0x00007ffd8eced000 	C:\WINDOWS\System32\USER32.dll
0x00007ffd8cd70000 - 0x00007ffd8cd92000 	C:\WINDOWS\System32\win32u.dll
0x00007ffd82a60000 - 0x00007ffd82cfa000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ffd8f1e0000 - 0x00007ffd8f20b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffd8f140000 - 0x00007ffd8f1de000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffd8d2c0000 - 0x00007ffd8d3d9000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffd8ced0000 - 0x00007ffd8cf6d000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffd8deb0000 - 0x00007ffd8dedf000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffd63320000 - 0x00007ffd6332c000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\vcruntime140_1.dll
0x00007ffd5d400000 - 0x00007ffd5d48d000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\msvcp140.dll
0x00007ffd113e0000 - 0x00007ffd12170000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\server\jvm.dll
0x00007ffd8e200000 - 0x00007ffd8e2b1000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffd8d940000 - 0x00007ffd8d9df000 	C:\WINDOWS\System32\sechost.dll
0x00007ffd8df70000 - 0x00007ffd8e093000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffd8cea0000 - 0x00007ffd8cec7000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffd8ed10000 - 0x00007ffd8ed7b000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffd8c1b0000 - 0x00007ffd8c1fb000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffd800d0000 - 0x00007ffd800f7000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffd87240000 - 0x00007ffd8724a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffd8c020000 - 0x00007ffd8c032000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffd8aaa0000 - 0x00007ffd8aab2000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffd63310000 - 0x00007ffd6331a000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\jimage.dll
0x00007ffd7f6f0000 - 0x00007ffd7f8f1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffd75c10000 - 0x00007ffd75c44000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffd8d3e0000 - 0x00007ffd8d462000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffd63220000 - 0x00007ffd6323f000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\java.dll
0x00007ffd8e330000 - 0x00007ffd8ea9e000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffd8aca0000 - 0x00007ffd8b444000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffd8f210000 - 0x00007ffd8f563000 	C:\WINDOWS\System32\combase.dll
0x00007ffd8c670000 - 0x00007ffd8c69b000 	C:\WINDOWS\SYSTEM32\Wldp.dll
0x00007ffd8e130000 - 0x00007ffd8e1fd000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffd8eaa0000 - 0x00007ffd8eb4d000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffd8ed80000 - 0x00007ffd8eddb000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffd8cb70000 - 0x00007ffd8cb95000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffd5a580000 - 0x00007ffd5a657000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\jsvml.dll
0x00007ffd63190000 - 0x00007ffd631a0000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\net.dll
0x00007ffd88d00000 - 0x00007ffd88e0a000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffd8c3d0000 - 0x00007ffd8c43a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffd60c80000 - 0x00007ffd60c96000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\nio.dll
0x00007ffd60270000 - 0x00007ffd60288000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\zip.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\server

VM Arguments:
jvm_args: -Xmx64m -Xms64m -Dorg.gradle.appname=gradlew 
java_command: org.gradle.wrapper.GradleWrapperMain assembleDebug
java_class_path (initial): C:\Users\<USER>\AndroidStudioProjects\scratch gpro\\gradle\wrapper\gradle-wrapper.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 67108864                                  {product} {command line}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 67108864                                  {product} {command line}
   size_t MaxNewSize                               = 39845888                                  {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 67108864                                  {product} {command line}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 67108864                               {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot
CLASSPATH=C:\Users\<USER>\AndroidStudioProjects\scratch gpro\\gradle\wrapper\gradle-wrapper.jar
PATH=C:\Users\<USER>\flutter\bin\cache\dart-sdk\bin\;C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Users\<USER>\AppData\Local\Android\Sdk\cmdline-tools\latest\bin;%ANDROID_HOME%\platform-tools;C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin;C:\Python313\Scripts\;C:\Python313\;C:\Program Files (x86)\Common Files\Intel\Shared Libraries\redist\intel64_win\compiler;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\Git\cmd;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\flutter\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Android\Sdk;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Muse Hub\lib;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Android\Sdk\tools;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AndroidStudioProjects\scratch gpro\flutter\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand
USERNAME=stant
LANG=en_US.UTF-8
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 8 Stepping 2, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 0 days 5:26 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (12 cores per cpu, 2 threads per core) family 23 model 8 stepping 2 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, hv, rdtscp, f16c
Processor Information for the first 12 processors :
  Max Mhz: 3600, Current Mhz: 3600, Mhz Limit: 3600

Memory: 4k page, system-wide physical 32716M (21769M free)
TotalPageFile size 51148M (AvailPageFile size 38544M)
current process WorkingSet (physical memory assigned to process): 67M, peak: 67M
current process commit charge ("private bytes"): 154M, peak: 155M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
