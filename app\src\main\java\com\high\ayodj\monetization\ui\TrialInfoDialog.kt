package com.high.ayodj.monetization.ui

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.high.ayodj.monetization.TrialManager

@Composable
fun TrialInfoDialog(
    trialManager: TrialManager,
    onDismiss: () -> Unit,
    onUpgradeClicked: () -> Unit
) {
    val daysRemaining = trialManager.getTrialDaysRemaining()
    val trialEndDate = trialManager.getTrialEndDate()
    
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Trial icon
                Icon(
                    imageVector = Icons.Default.Star,
                    contentDescription = null,
                    modifier = Modifier.size(48.dp),
                    tint = MaterialTheme.colorScheme.primary
                )
                
                // Title
                Text(
                    text = "Welcome to AyoDJ!",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center
                )
                
                // Trial info
                Text(
                    text = "You're on a FREE 2-week trial",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.primary,
                    fontWeight = FontWeight.Medium,
                    textAlign = TextAlign.Center
                )
                
                // What's included
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Text(
                            text = "Trial includes:",
                            style = MaterialTheme.typography.titleSmall,
                            fontWeight = FontWeight.Bold
                        )
                        
                        TrialFeatureItem(
                            icon = Icons.Default.PlayArrow,
                            text = "Full access to all weekly track drops"
                        )
                        
                        TrialFeatureItem(
                            icon = Icons.Default.PlayArrow,
                            text = "Complete scratch sample library"
                        )
                        
                        TrialFeatureItem(
                            icon = Icons.Default.Notifications,
                            text = "New release notifications"
                        )
                    }
                }
                
                // Limitations
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Text(
                            text = "Premium features:",
                            style = MaterialTheme.typography.titleSmall,
                            fontWeight = FontWeight.Bold
                        )
                        
                        TrialFeatureItem(
                            icon = Icons.Default.Add,
                            text = "Import your own audio files",
                            isPremium = true
                        )
                        
                        TrialFeatureItem(
                            icon = Icons.Default.CheckCircle,
                            text = "Save tracks permanently to device",
                            isPremium = true
                        )
                        
                        TrialFeatureItem(
                            icon = Icons.Default.Check,
                            text = "Access to full track backlog",
                            isPremium = true
                        )
                    }
                }
                
                // Time remaining
                Text(
                    text = "$daysRemaining days remaining in trial",
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center
                )
                
                // Action buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    TextButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("Start Trial")
                    }
                    
                    Button(
                        onClick = onUpgradeClicked,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = "Upgrade Now",
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun TrialFeatureItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    text: String,
    isPremium: Boolean = false
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Icon(
            imageVector = if (isPremium) Icons.Default.Lock else icon,
            contentDescription = null,
            modifier = Modifier.size(16.dp),
            tint = if (isPremium) 
                MaterialTheme.colorScheme.onSurfaceVariant 
            else MaterialTheme.colorScheme.primary
        )
        
        Text(
            text = text,
            style = MaterialTheme.typography.bodyMedium,
            color = if (isPremium) 
                MaterialTheme.colorScheme.onSurfaceVariant 
            else MaterialTheme.colorScheme.onSurface
        )
    }
}

@Composable
fun TrialStatusBanner(
    trialManager: TrialManager,
    subscriptionStatus: com.high.ayodj.monetization.SubscriptionStatus,
    onUpgradeClicked: () -> Unit,
    modifier: Modifier = Modifier
) {
    when (subscriptionStatus) {
        com.high.ayodj.monetization.SubscriptionStatus.TRIAL -> {
            // Normal trial banner
            Card(
                modifier = modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Star,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.primary
                    )
                    
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "Trial Active",
                            style = MaterialTheme.typography.titleSmall,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                        Text(
                            text = trialManager.getTrialTimeRemainingFormatted(),
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    }
                    
                    Button(
                        onClick = onUpgradeClicked,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.primary
                        )
                    ) {
                        Text(
                            text = "Upgrade",
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }
        }
        
        com.high.ayodj.monetization.SubscriptionStatus.FREE -> {
            // ANNOYING expired trial banner - TWICE AS BIG!
            Card(
                modifier = modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.errorContainer
                ),
                border = BorderStroke(2.dp, MaterialTheme.colorScheme.error)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(24.dp), // Double padding for bigger size
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // Top row with icons
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Warning,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.error,
                            modifier = Modifier.size(32.dp)
                        )
                        Text(
                            text = "🔥 TRIAL EXPIRED 🔥",
                            style = MaterialTheme.typography.headlineSmall,
                            fontWeight = FontWeight.ExtraBold,
                            color = MaterialTheme.colorScheme.error,
                            textAlign = TextAlign.Center
                        )
                        Icon(
                            imageVector = Icons.Default.Warning,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.error,
                            modifier = Modifier.size(32.dp)
                        )
                    }
                    
                    // Main message
                    Text(
                        text = "💰 BUY OUR PROGRAM! 💰",
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Black,
                        color = MaterialTheme.colorScheme.error,
                        textAlign = TextAlign.Center
                    )
                    
                    Text(
                        text = "Unlock premium tracks, file imports, and become a DJ legend!",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onErrorContainer,
                        textAlign = TextAlign.Center,
                        fontWeight = FontWeight.SemiBold
                    )
                    
                    // Big annoying button
                    Button(
                        onClick = onUpgradeClicked,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(56.dp), // Extra tall button
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.error
                        )
                    ) {
                        Text(
                            text = "🚀 UPGRADE NOW! 🚀",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.onError,
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }
        }
        
        else -> {
            // No banner for other subscription types
        }
    }
}
