package com.high.ayodj.fileloader

import android.content.Context
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

sealed class LoaderState {
    object Idle : LoaderState()
    object Loading : LoaderState()
    data class Success(val result: FileLoadResult) : LoaderState()
    data class Error(val error: String) : LoaderState()
}

data class CacheEntry(
    val key: String,
    val result: FileLoadResult,
    val timestamp: Long
)

object SampleLoader {
    private val cache = mutableMapOf<String, CacheEntry>()

    suspend fun loadSample(context: Context, request: FileLoadRequest): LoaderState = withContext(Dispatchers.IO) {
        val cacheKey = request.toString()
        cache[cacheKey]?.let {
            return@withContext LoaderState.Success(it.result)
        }
        val result = FileResolver.resolve(context, request)
        if (result.success) {
            cache[cacheKey] = CacheEntry(cacheKey, result, System.currentTimeMillis())
            LoaderState.Success(result)
        } else {
            LoaderState.Error(result.error ?: "Unknown error")
        }
    }
    fun clearCache() {
        cache.clear()
    }
}
