package com.high.ayodj

import android.content.Context
import android.util.Log
import android.widget.Toast

/**
 * Centralized error handling for production-quality error management
 */
object ErrorHandler {
    private const val TAG = "ScratchEmulator_Error"
    
    enum class ErrorSeverity {
        LOW,    // Minor issues that don't affect core functionality
        MEDIUM, // Issues that may affect user experience
        HIGH,   // Critical issues that affect core functionality
        CRITICAL // App-breaking issues
    }
    
    fun handleError(
        context: Context?,
        tag: String,
        message: String,
        throwable: Throwable? = null,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        showToUser: Boolean = true,
        userMessage: String? = null
    ) {
        // Log the error with appropriate level
        when (severity) {
            ErrorSeverity.LOW -> Log.d("$TAG:$tag", message, throwable)
            ErrorSeverity.MEDIUM -> Log.w("$TAG:$tag", message, throwable)
            ErrorSeverity.HIGH -> Log.e("$TAG:$tag", message, throwable)
            ErrorSeverity.CRITICAL -> Log.wtf("$TAG:$tag", message, throwable)
        }
        
        // Show user-friendly message if requested
        if (showToUser && context != null) {
            val displayMessage = userMessage ?: when (severity) {
                ErrorSeverity.LOW -> "Minor issue detected"
                ErrorSeverity.MEDIUM -> "Something went wrong, but you can continue"
                ErrorSeverity.HIGH -> "Error occurred - some features may not work"
                ErrorSeverity.CRITICAL -> "Critical error - app may need to restart"
            }
            
            val toastLength = if (severity >= ErrorSeverity.HIGH) Toast.LENGTH_LONG else Toast.LENGTH_SHORT
            Toast.makeText(context, displayMessage, toastLength).show()
        }
        
        // Could add crash reporting here (Firebase Crashlytics, Bugsnag, etc.)
        // reportToCrashlytics(tag, message, throwable, severity)
    }
    
    fun handleAudioError(
        context: Context?,
        operation: String,
        throwable: Throwable? = null,
        showToUser: Boolean = true
    ) {
        handleError(
            context = context,
            tag = "Audio",
            message = "Audio operation failed: $operation",
            throwable = throwable,
            severity = ErrorSeverity.HIGH,
            showToUser = showToUser,
            userMessage = "Audio error occurred. Please try again."
        )
    }
    
    fun handleBillingError(
        context: Context?,
        operation: String,
        throwable: Throwable? = null,
        showToUser: Boolean = true
    ) {
        handleError(
            context = context,
            tag = "Billing",
            message = "Billing operation failed: $operation",
            throwable = throwable,
            severity = ErrorSeverity.MEDIUM,
            showToUser = showToUser,
            userMessage = "Payment system error. Please try again later."
        )
    }
    
    fun handleFileError(
        context: Context?,
        operation: String,
        fileName: String? = null,
        throwable: Throwable? = null,
        showToUser: Boolean = true
    ) {
        val message = if (fileName != null) {
            "File operation failed for '$fileName': $operation"
        } else {
            "File operation failed: $operation"
        }
        
        handleError(
            context = context,
            tag = "File",
            message = message,
            throwable = throwable,
            severity = ErrorSeverity.MEDIUM,
            showToUser = showToUser,
            userMessage = "File access error. Please check file permissions and try again."
        )
    }
    
    fun handleNetworkError(
        context: Context?,
        operation: String,
        throwable: Throwable? = null,
        showToUser: Boolean = true
    ) {
        handleError(
            context = context,
            tag = "Network",
            message = "Network operation failed: $operation",
            throwable = throwable,
            severity = ErrorSeverity.MEDIUM,
            showToUser = showToUser,
            userMessage = "Network connection error. Please check your internet connection."
        )
    }
}
