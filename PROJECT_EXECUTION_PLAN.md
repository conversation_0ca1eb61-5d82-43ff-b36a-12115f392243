# AyoDJ Execution Plan

_Last updated: 2025-08-12_

## Purpose
Structured, incremental delivery of the current TODO items with clear acceptance criteria, instrumentation, risk tracking, and decision history. This file is the working hub; keep concise updates (append, don’t overwrite history sections).

## High-Level Goals
1. Eliminate intro sound double-play.
2. Provide adjustable & unified max scratch speed (visual + audio) via settings.
3. Ship proper branded launcher & notification icons.
4. Polish notification + track loading system for release readiness.
5. Diagnose & mitigate early playback artifacts (~2 min pops/clicks) and longer-session stability (buffer overruns, leaks, drift).

---
## Task Matrix
| ID | Title | Status | Owner | Target Build | Acceptance Snapshot |
|----|-------|--------|-------|--------------|---------------------|
| 1 | Intro sound plays twice | In-Progress |  | Next patch | Single play only across cold start; no duplicate logs; regression test script passes |
| 2 | Adjustable max scratch speed | In-Progress |  | +1 patch | Slider persists; native cap updates immediately; visual & audio alignment < 2° drift over 30s rapid moves |
| 3 | App launcher/logo integration | Planned |  | +1 patch | Launcher shows custom icon on fresh install & after cache clear; adaptive & monochrome variants pass lint |
| 4 | Notification + track loading polish | Planned |  | +2 patch | Consistent channel config; no duplicate track notifications; dismiss logic verified; background fetch stable |
| 5 | Early artifact & stability investigation | Planned |  | Rolling | No pops/clicks during first 5 min stress; 2h soak test free of glitches; no rising native alloc trend; drift > 5° avoided |

Status Legend: Planned | In-Progress | Review | Done | Blocked

---
## Detailed Work Items
### 1. Intro Sound Double-Play
- Hypotheses:
  - Sample auto-loaded again as first platter sample (path collision `sounds/haahhh`).
  - Re-trigger due to lifecycle (ViewModel re-init on config change) before engine finishes first play.
- Actions:
  - Add `hasIntroPlayed` flag in `AppViewModel` (DONE; in-memory only).
  - Skip including `INTRO_SAMPLE_PATH` in auto-load user list when choosing first sample. (PENDING explicit filter)
  - Native guard: If `activePlatterSample_` already set with `playOnceThenLoopSilently` and not `playedOnce`, ignore further intro calls. (DONE)
- Acceptance Tests:
  - Cold start log shows exactly one `enginePlayIntroAndLoopOnPlatter` and one "Intro ASSET sample ... set up".
  - Rotating device (config change) does NOT replay intro.

### 2. Adjustable Max Scratch Speed
- Current: Hardcoded `MAX_PLATTER_SPEED = 1.5f` in `AudioEngine.h`.
- Design:
  - Replace constant with `std::atomic<float> maxPlatterSpeed_` default 1.5f. (DONE)
  - JNI: `setMaxPlatterSpeed(float)` updates atomic & returns clamped value. (DONE)
  - `limitPlatterSpeed()` uses current atomic. (DONE)
  - ViewModel state `maxScratchSpeed`, persisted to SharedPreferences (range 0.5–3.0, default 1.5). (DONE)
  - Settings UI: Slider + numeric label. Visual mapping updated instantly. (DONE)
- Metrics:
  - Log once on change: requested vs applied speed.
  - Drift test: Move platter rapidly ± full range for 30s; audio vs UI angle drift < 2°.

### 3. App Icon & Branding ✅ COMPLETED
- Steps:
  - ✅ Added branded assets: drawable/ic_launcher_background.png (from BG.png), ic_launcher_foreground.png (from logo.png).
  - ✅ Updated adaptive icon XML (mipmap-anydpi-v26 & mipmap-anydpi) to reference @drawable layers.
  - ✅ Added complete legacy bitmap densities (mdpi→xxxhdpi) from legacy.png for pre-Oreo support.
  - ✅ Removed old template vector drawables and conflicting webp files.
  - ✅ Fixed themed icon issue by removing monochrome layer (prevents auto-greyscale conversion).
  - ✅ Small notification icon updated previously to ic_notification.
  - ✅ Verified on device: full-color icon displays correctly in app drawer and home screen.
- Acceptance:
  - No lint errors for adaptive icons; colorful logo preserved across all launcher contexts.

### 4. Notification & Track Loading Polish
- Current Observations: Using launcher icon for all notifications; multiple dismissal pathways; ensure no spam.
- Actions:
  - Create channels: (updates, promos, tracks) with appropriate importance (only tracks high).
  - Distinct small icon + optional large icon for promo.
  - Debounce logic: Limit track notification generation to once per new track ID until user opens track list.
  - Wrap verbose logs under `if (BuildConfig.DEBUG)`. 
  - Add unit-style test (instrumented) for notification flow (optional if time).
- Acceptance:
  - Trigger simulation (existing `NotificationTestUtils`) shows one notification per category.
  - Clearing / opening app dismisses track notification reliably.

### 5. Early Artifact (2‑Minute) & Long-Session Stability Investigation
Observed: Audible pops/clicks emerging as early as ~2 minutes into continuous scratching (not only truly long sessions). We still run an extended stability ("soak") test afterward.

Hypotheses (early phase):
 - Buffer boundary wrap condition (frame index crossing) produces discontinuity.
 - Rate smoothing filter (`SPEED_LIMIT_SMOOTHING`) introducing tiny discontinuities when user rapidly reverses direction.
 - Atomic snapshot / playbackRateToUse mismatch mid-buffer causing frame jump.
 - Denormals or floating point precision near loop boundary (need fmod safeguards) creating audible click.

Additional Long-Run Risks:
 - Memory churn in sample caches.
 - Accumulating drift between UI & audio angle after many wraps.

Instrumentation Plan (phased):
 - First 5 minutes: capture high-frequency metrics every 30s (frame position, delta between successive buffers' start frame, any large jump > 1024 frames, underrun count if Oboe exposes, limited speed clamp count).
 - After 5 minutes: reduce to every 5 minutes (as originally planned) for stability.
 - Add counters: underruns, large frame jumps (> 2048 frames), wrap anomalies (extensive wrap log already present), click suspects (flag when absolute sample discontinuity > threshold between adjacent frames if we add simple monitor in debug build).
 - JNI debug snapshot method returning JSON of current metrics for potential UI display.

Test Protocols:
 - Smoke Test (quick): 3-minute manual rapid scratch & direction changes looking for immediate artifacts.
 - Diagnostic Repro Script: scripted ±max speed sweeps at 2Hz for 3 minutes to force frequent boundary crossings.
 - Soak (Extended) Test: 2-hour automated pattern (retain term "soak" though earlier we referred to smoke; both used intentionally—smoke for quick sanity, soak for longevity).

Exit Criteria:
 - No pops/clicks in Smoke + Diagnostic tests (subjective + absence of large discontinuity logs).
 - Early (first 5 min) metrics show zero large frame jump events.
 - Over 2h: no increasing trend (>10%) in memory metrics; no cumulative drift > 5°; underrun counter stable (ideally zero).

---
## Preferences Keys (New / Planned)
- `maxScratchSpeed` (Float) – adjustable max scratch speed.

---
## Risk Register
| Risk | Impact | Likelihood | Mitigation |
|------|--------|------------|------------|
| Native max speed change introduces instability | Medium | Low | Keep default same; gate behind range clamp, add logs |
| Intro guard misses lifecycle edge case | Low | Medium | Add native guard AND Kotlin flag |
| Icon asset mismatch causes Play rejection | High | Low | Validate with lint & adaptive spec |
| Instrumentation overhead affects latency | Medium | Low | Throttle to 5–10 min intervals, compile out in release |

---
## Metrics & Observability
| Metric | Source | Frequency | Purpose |
|--------|--------|-----------|---------|
| Angle drift (UI vs audio) | compare callbacks | On change event & 30s interval | Validate sync integrity |
| Limited speed occurrences | limitPlatterSpeed() | On clamp | Tune default range |
| Large frame jump count | AudioEngine instrumentation | 30s first 5 min, then 5 min | Detect discontinuities causing clicks |
| Cache size (platter/music) | AudioEngine | 5 min | Detect leak/retention |
| Memory snapshot | Native instrumentation | 30s (first 5 min) then 5–10 min | Detect early leak & long-run trend |
| Underrun count | Oboe stream if exposed | 30s | Correlate pops with underruns |

---
## Implementation Sequencing
1. Intro guard (fast win, low risk) – PR 1.
2. Adjustable max speed backend (native + JNI) – PR 2.
3. Settings UI + persistence for max speed – PR 3.
4. Branding assets + manifest update – PR 4.
5. Notification polish (channels, icons, debounce) – PR 5.
6. Instrumentation + smoke/soak test script scaffold – PR 6.
7. Review & tighten logs / remove noisy debug – PR 7.

Parallelizable: 3 & 4, 5 & 6 (if bandwidth available).

---
## Decision Log
| Date | Decision | Rationale | Impact |
|------|----------|-----------|--------|
| 2025-08-12 | Store max speed in prefs, not compile-time | User customization & future tuning | Enables runtime experimentation |
| 2025-08-12 | Add both Kotlin & native intro guards | Defense in depth | Prevents regression from re-init |

(Add future decisions here, append only.)

---
## Verification Checklist (Running)
- [ ] Build passes (assembleDebug) after each PR.
- [ ] Lint / detekt / ktlint (if configured) clean for touched files.
- [ ] Native compilation warnings reviewed (none new introduced).
- [ ] Manual cold start test executed.
- [ ] Automated angle drift test (script pending) passes.

---
## Open Questions
- Provide final branded assets? (Need file names & color requirements.)
- Desired default max scratch speed beyond 1.5? (User feedback?)
- Target minimum Android SDK for notification channel grouping? (Assume 26+ already.)

---
## Next Immediate Actions
- Implement Task 1 & skeleton for Task 2.
- Update this file with status -> In-Progress after changes.

---
(End of document)
