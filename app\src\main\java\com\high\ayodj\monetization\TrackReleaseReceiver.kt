package com.high.ayodj.monetization

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.high.ayodj.MainActivity
import com.high.ayodj.R

/**
 * BroadcastReceiver that handles track release notifications
 * Triggered by AlarmManager at scheduled times
 */
class TrackReleaseReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "TrackReleaseReceiver"
        private const val CHANNEL_ID = "track_releases"
        private const val CHANNEL_NAME = "Track Releases"
        private const val CHANNEL_DESCRIPTION = "Notifications for new track releases"
        private const val NOTIFICATION_ID_BASE = 1000
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "Notification trigger received")
        
        val trackId = intent.getStringExtra("track_id")
        val trackTitle = intent.getStringExtra("track_title") ?: "New Track"
        val trackArtist = intent.getStringExtra("track_artist") ?: "AyoDJ"
        val notificationMessage = intent.getStringExtra("notification_message") ?: "New track available!"
        val isWeekendReminder = intent.getBooleanExtra("is_weekend_reminder", false)
        val isBetaTest = intent.getBooleanExtra("is_beta_test", false)
        
        // Create notification channel if needed
        createNotificationChannel(context)
        
        if (isWeekendReminder) {
            showWeekendReminderNotification(context, notificationMessage)
        } else if (trackId != null) {
            // Unlock the track
            unlockTrack(context, trackId)
            
            // Show notification
            showTrackReleaseNotification(
                context = context,
                trackId = trackId,
                trackTitle = trackTitle,
                trackArtist = trackArtist,
                message = notificationMessage,
                isBetaTest = isBetaTest
            )
        } else {
            Log.w(TAG, "Received notification trigger without track ID")
        }
    }
    
    /**
     * Unlock the track in the database
     */
    private fun unlockTrack(context: Context, trackId: String) {
        try {
            val trackManager = TrackManager(context)
            trackManager.unlockTrack(trackId)
            Log.i(TAG, "Track unlocked: $trackId")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to unlock track: $trackId", e)
        }
    }
    
    /**
     * Show notification for new track release
     */
    private fun showTrackReleaseNotification(
        context: Context,
        trackId: String,
        trackTitle: String,
        trackArtist: String,
        message: String,
        isBetaTest: Boolean
    ) {
        // Create intent to open the app
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("open_track_id", trackId)
            putExtra("from_notification", true)
        }
        
        val pendingIntent = PendingIntent.getActivity(
            context,
            trackId.hashCode(),
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notificationTitle = if (isBetaTest) "🧪 BETA: $message" else message
        val notificationText = "$trackTitle by $trackArtist"
        
        val notification = NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification) // You'll need to add this icon
            .setContentTitle(notificationTitle)
            .setContentText(notificationText)
            .setStyle(
                NotificationCompat.BigTextStyle()
                    .bigText("$notificationText\n\nTap to open AyoDJ and start scratching!")
            )
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setDefaults(NotificationCompat.DEFAULT_ALL)
            .setCategory(NotificationCompat.CATEGORY_SOCIAL)
            .build()
        
        try {
            NotificationManagerCompat.from(context).notify(
                NOTIFICATION_ID_BASE + trackId.hashCode(),
                notification
            )
            Log.i(TAG, "Track release notification shown for: $trackId")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to show notification for track: $trackId", e)
        }
    }
    
    /**
     * Show weekend reminder notification
     */
    private fun showWeekendReminderNotification(context: Context, message: String) {
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("open_tracks_list", true)
            putExtra("from_notification", true)
        }
        
        val pendingIntent = PendingIntent.getActivity(
            context,
            "weekend_reminder".hashCode(),
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle("AyoDJ Weekend Vibes")
            .setContentText(message)
            .setStyle(
                NotificationCompat.BigTextStyle()
                    .bigText("$message\n\nCheck out this week's releases before they expire!")
            )
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .build()
        
        try {
            NotificationManagerCompat.from(context).notify(
                NOTIFICATION_ID_BASE + "weekend".hashCode(),
                notification
            )
            Log.i(TAG, "Weekend reminder notification shown")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to show weekend reminder notification", e)
        }
    }
    
    /**
     * Create notification channel for Android 8.0+
     */
    private fun createNotificationChannel(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = CHANNEL_DESCRIPTION
                enableVibration(true)
                enableLights(true)
                setShowBadge(true)
            }
            
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
            Log.d(TAG, "Notification channel created")
        }
    }
}
