package com.high.ayodj

enum class TrackSource {
    USER_ADDED,     // User imported files
    AYO_OFFICIAL    // Official Ayo tracks (have URL in comment metadata)
}

data class UserSoundItem(
    val filePath: String,
    val displayName: String,
    val saveToAutoLoad: <PERSON><PERSON>an,
    val isHardcoded: <PERSON><PERSON>an,
    val modificationTimestamp: Long = 1L, // Default for assets
    val title: String? = null, // MP3 metadata title
    val artist: String? = null, // MP3 metadata artist  
    val durationSeconds: Int = 0, // MP3 duration in seconds
    val source: TrackSource = TrackSource.USER_ADDED // Track source identifier
)
