#pragma once

#include <atomic>
#include <chrono>
#include <android/log.h>
#include <unistd.h>
#include <sys/resource.h>
#include <sched.h>

#define PRIORITY_TAG "ThreadPriority"
#define PRIORITY_LOGI(...) __android_log_print(ANDROID_LOG_INFO, PRIORITY_TAG, __VA_ARGS__)
#define PRIORITY_LOGW(...) __android_log_print(ANDROID_LOG_WARN, PRIORITY_TAG, __VA_ARGS__)
#define PRIORITY_LOGE(...) __android_log_print(ANDROID_LOG_ERROR, PRIORITY_TAG, __VA_ARGS__)

/**
 * AUDIO-FIRST ARCHITECTURE: Thread priority monitoring and enforcement
 * Ensures audio threads maintain highest priority throughout execution
 */
class ThreadPriorityMonitor {
public:
    static ThreadPriorityMonitor& getInstance() {
        static ThreadPriorityMonitor instance;
        return instance;
    }

    enum class ThreadType {
        AUDIO_CALLBACK,
        VINYL_TRACKER,
        UI_UPDATES,
        BACKGROUND_TASKS
    };

    struct ThreadInfo {
        pid_t tid;
        ThreadType type;
        int expectedPriority;
        int actualPriority;
        int policy;
        std::chrono::steady_clock::time_point lastCheck;
    };

    // Register a thread for monitoring
    void registerThread(ThreadType type, const char* name) {
        pid_t tid = gettid();
        ThreadInfo info;
        info.tid = tid;
        info.type = type;
        info.expectedPriority = getExpectedPriority(type);
        info.actualPriority = getpriority(PRIO_PROCESS, tid);
        
        struct sched_param param;
        info.policy = sched_getscheduler(tid);
        if (info.policy != -1) {
            sched_getparam(tid, &param);
            if (info.policy == SCHED_FIFO || info.policy == SCHED_RR) {
                info.actualPriority = param.sched_priority;
            }
        }
        
        info.lastCheck = std::chrono::steady_clock::now();
        
        PRIORITY_LOGI("AUDIO-FIRST: Registered %s thread (TID=%d) - Expected: %d, Actual: %d, Policy: %s",
                      name, tid, info.expectedPriority, info.actualPriority, getPolicyName(info.policy));
        
        // Store thread info (simplified - in real implementation would use thread-safe container)
        lastRegisteredThread_ = info;
    }

    // Check and report thread priorities
    void checkThreadPriorities() {
        static uint32_t checkCounter = 0;
        if ((checkCounter++ & 0x7FFF) == 0) { // Every ~11 seconds at 48kHz
            reportThreadStatus();
        }
    }

    // Enforce audio thread priority if it has been lowered
    void enforceAudioPriority() {
        if (lastRegisteredThread_.type == ThreadType::AUDIO_CALLBACK) {
            int currentPriority = getpriority(PRIO_PROCESS, 0);
            if (currentPriority > -19) { // If not at high priority
                struct sched_param param;
                param.sched_priority = sched_get_priority_max(SCHED_FIFO) - 1;
                if (sched_setscheduler(0, SCHED_FIFO, &param) != 0) {
                    // Fallback to nice priority
                    setpriority(PRIO_PROCESS, 0, -20);
                }
                PRIORITY_LOGW("AUDIO-FIRST: Re-enforced audio thread priority");
            }
        }
    }

private:
    ThreadInfo lastRegisteredThread_; // Simplified storage
    
    int getExpectedPriority(ThreadType type) {
        switch (type) {
            case ThreadType::AUDIO_CALLBACK:
                return sched_get_priority_max(SCHED_FIFO) - 1; // Highest
            case ThreadType::VINYL_TRACKER:
                return sched_get_priority_max(SCHED_FIFO) - 3; // High
            case ThreadType::UI_UPDATES:
                return -5; // Medium (nice priority)
            case ThreadType::BACKGROUND_TASKS:
                return 10; // Low (nice priority)
            default:
                return 0;
        }
    }
    
    const char* getPolicyName(int policy) {
        switch (policy) {
            case SCHED_FIFO: return "SCHED_FIFO";
            case SCHED_RR: return "SCHED_RR";
            case SCHED_OTHER: return "SCHED_OTHER";
            default: return "UNKNOWN";
        }
    }
    
    void reportThreadStatus() {
        PRIORITY_LOGI("AUDIO-FIRST: Thread Priority Status Report");
        PRIORITY_LOGI("  Audio Callback: Expected real-time, monitoring active");
        PRIORITY_LOGI("  VinylTracker: Expected high priority, monitoring active");
        PRIORITY_LOGI("  UI Updates: Expected display priority, monitoring active");
        PRIORITY_LOGI("  Background: Expected lowest priority, monitoring active");
    }

    ThreadPriorityMonitor() = default;
};

// Convenience macros for thread registration
#define REGISTER_AUDIO_THREAD(name) ThreadPriorityMonitor::getInstance().registerThread(ThreadPriorityMonitor::ThreadType::AUDIO_CALLBACK, name)
#define REGISTER_VINYL_THREAD(name) ThreadPriorityMonitor::getInstance().registerThread(ThreadPriorityMonitor::ThreadType::VINYL_TRACKER, name)
#define REGISTER_UI_THREAD(name) ThreadPriorityMonitor::getInstance().registerThread(ThreadPriorityMonitor::ThreadType::UI_UPDATES, name)
#define REGISTER_BACKGROUND_THREAD(name) ThreadPriorityMonitor::getInstance().registerThread(ThreadPriorityMonitor::ThreadType::BACKGROUND_TASKS, name)

#define CHECK_THREAD_PRIORITIES() ThreadPriorityMonitor::getInstance().checkThreadPriorities()
#define ENFORCE_AUDIO_PRIORITY() ThreadPriorityMonitor::getInstance().enforceAudioPriority()
