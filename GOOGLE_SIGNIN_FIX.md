# 🔧 Google Sign-In Fix Guide

## The Problem
Your `google-services.json` is missing OAuth 2.0 client credentials, which is why Google Sign-In fails.

## ✅ Solution Steps

### Step 1: Configure OAuth in Firebase Console

1. **Go to Firebase Console**: https://console.firebase.google.com/
2. **Select your project**: `ayodj-b5cb9d32`
3. **Go to Project Settings** (gear icon)
4. **Go to "Your apps" tab**
5. **Find your Android app** (`com.high.ayodj`)

### Step 2: Add SHA-1 Fingerprints

**Debug SHA-1 (for testing):**
```
19:54:D7:58:0B:DD:D9:FA:52:F0:5C:13:73:70:E8:53:08:D4:01:02
```

**For Release (you'll need to create a keystore):**
1. Click "Add fingerprint"
2. Add the debug SHA-1 above
3. If you have a release keystore, add that SHA-1 too

### Step 3: Enable Google Sign-In

1. **In Firebase Console** → **Authentication**
2. **Sign-in method** tab
3. **Enable Google** sign-in provider
4. **Add your support email**

### Step 4: Download New google-services.json

1. **Back to Project Settings**
2. **Download the updated `google-services.json`**
3. **Replace** your current file with the new one

The new file should have an `oauth_client` section like this:
```json
"oauth_client": [
  {
    "client_id": "...your-client-id...",
    "client_type": 3
  }
]
```

### Step 5: Create Release Keystore (Important!)

Since you don't have a release keystore, let's create one:

```bash
keytool -genkey -v -keystore ayodj-release-key.keystore -alias ayodj -keyalg RSA -keysize 2048 -validity 10000
```

**Important Info to Remember:**
- Store this keystore file safely
- Remember the passwords
- You'll need this for Google Play uploads

### Step 6: Update build.gradle.kts for Release Signing

Add this to your `app/build.gradle.kts`:

```kotlin
android {
    signingConfigs {
        create("release") {
            storeFile = file("../ayodj-release-key.keystore")
            storePassword = "your-store-password"
            keyAlias = "ayodj"
            keyPassword = "your-key-password"
        }
    }
    
    buildTypes {
        release {
            signingConfig = signingConfigs.getByName("release")
            // ... rest of your release config
        }
    }
}
```

## 🧪 Testing the Fix

1. **Update google-services.json** with OAuth client
2. **Clean and rebuild**: `./gradlew clean assembleDebug`
3. **Test Google Sign-In** in your app
4. **Check logs** for any remaining errors

## Common Issues

**"Developer Error" or "Sign-in failed":**
- SHA-1 fingerprint not added to Firebase
- Package name mismatch
- OAuth client not configured

**"Network Error":**
- Check internet connection
- Verify Firebase project is active

## Quick Test

Your CloudSyncManager looks correct. Once you update the google-services.json with proper OAuth credentials, Google Sign-In should work immediately.

The error you're seeing is likely:
- Status Code: 10 (Developer Error)
- This means OAuth client is not configured

Follow the steps above and Google Sign-In will work perfectly! 🚀
