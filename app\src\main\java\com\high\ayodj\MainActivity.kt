package com.high.ayodj

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.content.res.Configuration
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.OpenableColumns
import android.util.Log
import android.view.View
import android.view.WindowInsets
import android.view.WindowInsetsController
import android.view.WindowManager
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.firebase.messaging.FirebaseMessaging

class MainActivity : ComponentActivity() {

    companion object {
        var isCurrentUserPremium = false // This should be managed by billing system
        private const val NOTIFICATION_PERMISSION_REQUEST_CODE = 1001
        
        init {
            System.loadLibrary("native-lib")
        }
    }

    private lateinit var billingClientWrapper: BillingClientWrapper
    private lateinit var cloudSyncManager: CloudSyncManager
    
    // File picker launcher
    private val filePickerLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                handleSelectedFile(uri)
            }
        }
    }

    private val googleSignInLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        val data: Intent? = result.data
        val task = GoogleSignIn.getSignedInAccountFromIntent(data)
        appViewModel.handleGoogleSignInResult(task)
    }
    
    private var currentSoundTypeForPicker: SoundType? = null

    // Native method declarations
    external fun initAudioEngine(assetManager: android.content.res.AssetManager, cacheDir: String, mainActivity: MainActivity)
    external fun startPlayback()
    external fun stopPlayback()
    external fun releaseAudioEngine()
    external fun stringFromJNI(): String
    
    // Audio loading methods
    external fun playIntroAndLoopOnPlatter(assetManager: android.content.res.AssetManager, assetPath: String, appVersionCode: Long)
    external fun jniLoadAssetPlatterSample(assetPath: String, appVersionCode: Long)
    external fun jniLoadAssetMusicTrack(assetPath: String, appVersionCode: Long)
    external fun loadUserPlatterSample(filePathOrUri: String, fd: Int, offset: Long, length: Long, modificationTimestamp: Long)
    external fun loadUserMusicTrack(filePathOrUri: String, fd: Int, offset: Long, length: Long, modificationTimestamp: Long)
    external fun stopMusicTrack()
    
    // Control methods
    external fun setPlatterFaderVolume(volume: Float)
    external fun setMusicMasterVolume(volume: Float)
    external fun scratchPlatterActive(isActive: Boolean, velocity: Float)
    external fun releasePlatterTouch()

    external fun setAudioNormalizationFactor(degreesPerFrame: Float)
    
    // VinylTracker control methods
    external fun startVinylTracking()
    external fun stopVinylTracking()
    external fun getCurrentVinylAngle(): Float
    external fun getCurrentAudioAngleDegrees(): Float
    external fun isVinylTracking(): Boolean
    external fun getUnwrappedVinylAngle(): Float
    external fun getTotalRotations(): Float // NEW: Get cumulative rotation count
    external fun resetVinylAngle()
    external fun requestVinylSync() // Request precise sync from audio callback

    // Tick-based timing methods - STEP 1: Enable for testing
    external fun getCurrentTickBasedVinylAngle(): Float
    external fun getCurrentMasterTick(): Long

    // SINGLE SOURCE OF TRUTH: SlipmatPhysics control methods
    external fun setSlipmatDamping(damping: Float)
    external fun setSlipmatAbruptness(abruptness: Float)
    external fun setSlipmatBaseFriction(baseFriction: Float)
    external fun getCurrentSlipmatSpeed(): Float
    external fun getTargetSlipmatSpeed(): Float
    external fun setMaxPlatterSpeed(newMax: Float)
    external fun getMaxPlatterSpeed(): Float

    // Debug method to compare angle systems
    fun compareAngleSystems() {
        val floatAngle = getCurrentVinylAngle()
        val tickAngle = getCurrentTickBasedVinylAngle()
        val masterTick = getCurrentMasterTick()
        Log.i("TickTest", "Float angle: $floatAngle°, Tick angle: $tickAngle°, Master tick: $masterTick")
    }

    // ADAPTIVE SYNC: Periodic testing with dynamic frequency based on activity
    private fun startTickSystemTesting() {
        val handler = android.os.Handler(android.os.Looper.getMainLooper())
        val runnable = object : Runnable {
            override fun run() {
                try {
                    compareAngleSystems()
                } catch (e: Exception) {
                    Log.w("TickTest", "Error comparing angle systems: ${e.message}")
                }
                
                // Fixed frequency for sync verification (adaptive frequency optimization for later)
                val delayMs = 3000L // 3 seconds for stable monitoring
                handler.postDelayed(this, delayMs)
            }
        }
        handler.postDelayed(runnable, 5000) // Start after 5 seconds
    }

    private val appViewModel: AppViewModel by viewModels {
        object : ViewModelProvider.Factory {
            @Suppress("UNCHECKED_CAST")
            override fun <T : ViewModel> create(modelClass: Class<T>): T {
                val vm: AppViewModel = AppViewModel(
                    application = application,
                    cloudSyncManager = cloudSyncManager,
                    signInLauncher = googleSignInLauncher,
                    onPlayIntroAndLoopOnPlatter = { filePath ->
                        Log.d("MainActivity", "Loading intro sample: $filePath")
                        playIntroAndLoopOnPlatter(assets, filePath, getAppVersionCode())
                    },
                    onLoadUserPlatterSample = { uriString, modTime ->
                        Log.d("MainActivity", "Loading user platter sample: $uriString")
                        loadUserFileWithDescriptor(uriString, true, modTime)
                    },
                    onLoadAssetPlatterSample = { assetPath ->
                        Log.d("MainActivity", "Loading asset platter sample: $assetPath")
                        jniLoadAssetPlatterSample(assetPath, getAppVersionCode())
                    },
                    onStopMusicTrack = {
                        Log.d("MainActivity", "Stopping music track")
                        stopMusicTrack()
                    },
                    onLoadUserMusicTrack = { uriString, modTime ->
                        Log.d("MainActivity", "Loading user music track: $uriString")
                        loadUserFileWithDescriptor(uriString, false, modTime)
                    },
                    onLoadAssetMusicTrack = { assetPath ->
                        Log.d("MainActivity", "Loading asset music track: $assetPath")
                        jniLoadAssetMusicTrack(assetPath, getAppVersionCode())
                    },
                    onUpdatePlatterFaderVolume = { volume ->
                        setPlatterFaderVolume(volume)
                    },
                    onUpdateMusicMasterVolume = { volume ->
                        setMusicMasterVolume(volume)
                    },
                    onScratchPlatterActive = { isActive, angleDeltaOrRate ->
                        scratchPlatterActive(isActive, angleDeltaOrRate)
                    },
                    onReleasePlatterTouch = {
                        releasePlatterTouch()
                    },
                    onRequestVinylSync = {
                        requestVinylSync()
                    },
                    onGetCurrentTickBasedVinylAngle = {
                        getCurrentTickBasedVinylAngle()
                    },
                    onGetCurrentVinylAngle = {
                        getCurrentVinylAngle()
                    },
                    onGetCurrentAudioAngleDegrees = {
                        getCurrentAudioAngleDegrees()
                    },
                    onGetUnwrappedVinylAngle = {
                        getUnwrappedVinylAngle()
                    },
                    onGetTotalRotations = {
                        getTotalRotations()
                    },

                    onSetAudioNormalizationFactor = { degrees ->
                        setAudioNormalizationFactor(degrees)
                    },
                    onOpenFilePicker = { soundType ->
                        openFilePicker(soundType)
                    },

                    // SINGLE SOURCE OF TRUTH: SlipmatPhysics control methods
                    onSetSlipmatDamping = { damping ->
                        Log.i("MainActivity_JNI", "onSetSlipmatDamping called with: $damping")
                        setSlipmatDamping(damping)
                        Log.i("MainActivity_JNI", "setSlipmatDamping completed")
                    },
                    onSetSlipmatAbruptness = { abruptness ->
                        Log.i("MainActivity_JNI", "onSetSlipmatAbruptness called with: $abruptness")
                        setSlipmatAbruptness(abruptness)
                        Log.i("MainActivity_JNI", "setSlipmatAbruptness completed")
                    },
                    onSetSlipmatBaseFriction = { baseFriction ->
                        Log.i("MainActivity_JNI", "onSetSlipmatBaseFriction called with: $baseFriction")
                        setSlipmatBaseFriction(baseFriction)
                        Log.i("MainActivity_JNI", "setSlipmatBaseFriction completed")
                    },
                    onGetCurrentSlipmatSpeed = {
                        getCurrentSlipmatSpeed()
                    },
                    onGetTargetSlipmatSpeed = {
                        getTargetSlipmatSpeed()
                    }
                )
                // Provide callback to push max scratch speed to native layer
                vm.applyMaxScratchSpeedToNative = { speed: Float ->
                    try {
                        setMaxPlatterSpeed(speed)
                    } catch (e: Exception) {
                        Log.e("MainActivity", "Failed to set max platter speed", e)
                    }
                }
                @Suppress("UNCHECKED_CAST")
                return vm as T
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Force app to always use dark theme regardless of system setting
        AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES)
        
        // Request exclusive audio focus to stop other audio apps
        val audioManager = getSystemService(AUDIO_SERVICE) as? android.media.AudioManager
        audioManager?.requestAudioFocus(null, android.media.AudioManager.STREAM_MUSIC, android.media.AudioManager.AUDIOFOCUS_GAIN)

        // Initialize CloudSyncManager first
        cloudSyncManager = CloudSyncManager(this)

        // Initialize audio engine
        try {
            initAudioEngine(assets, cacheDir.absolutePath, this)
            startPlayback()
            Log.i("MainActivity", "Audio engine initialized successfully")
            
            // Test VinylTracker functionality
            testVinylTracker()
        } catch (e: Exception) {
            Log.e("MainActivity", "Failed to initialize audio engine", e)
        }

        billingClientWrapper = BillingClientWrapper(
            context = this,
            onPurchaseComplete = { purchase ->
                // Handle successful purchase - update subscription status based on product
                when {
                    purchase.products.contains("upload_with_month_access") -> {
                        // User purchased upload access + 1 month premium
                        appViewModel.handleUploadAccessPurchase()
                        Log.i("MainActivity", "Upload + Month access purchased")
                    }
                    purchase.products.contains("premium_monthly_subscription") -> {
                        // User subscribed to premium monthly
                        appViewModel.handlePremiumSubscriptionPurchase()
                        Log.i("MainActivity", "Premium monthly subscription activated")
                    }
                }
            },
            onPurchaseFailed = { billingResult ->
                // Handle failed purchase
                Log.w("MainActivity", "Purchase failed: ${billingResult.debugMessage}")
            }
        )
        billingClientWrapper.startConnection()

        // Initialize Firebase Messaging and request notification permissions
        initializeFirebaseMessaging()
        
        // Log FCM token for testing
        NotificationTestUtils.logCurrentToken()

        setContent {
            DjApp(viewModel = appViewModel, billingClientWrapper = billingClientWrapper)
        }

        // STEP 1: Start periodic tick system comparison for testing
        startTickSystemTesting()

        // Enable immersive mode after window is fully initialized
        enableImmersiveMode()
    }

    override fun onDestroy() {
        super.onDestroy()
        try {
            stopPlayback()
            releaseAudioEngine()
            Log.i("MainActivity", "Audio engine released")
        } catch (e: Exception) {
            Log.e("MainActivity", "Error releasing audio engine", e)
        }
    }

    private fun getAppVersionCode(): Long {
        return try {
            packageManager.getPackageInfo(packageName, 0).longVersionCode
        } catch (e: Exception) {
            Log.w("MainActivity", "Could not get version code, using 1", e)
            1L
        }
    }

    private fun openFilePicker(soundType: SoundType) {
        currentSoundTypeForPicker = soundType
        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
            type = "audio/*"
            addCategory(Intent.CATEGORY_OPENABLE)
        }
        filePickerLauncher.launch(intent)
    }

    private fun handleSelectedFile(uri: Uri) {
        val soundType = currentSoundTypeForPicker ?: return
        currentSoundTypeForPicker = null

        // Take persistable URI permission
        val takeFlags: Int = Intent.FLAG_GRANT_READ_URI_PERMISSION
        contentResolver.takePersistableUriPermission(uri, takeFlags)

        // Get display name and modification time
        val fileDetails = getFileDetailsFromUri(uri)
        val displayName = fileDetails?.first ?: "Unknown File"
        val modTime = fileDetails?.second ?: System.currentTimeMillis()
        
        // Add to ViewModel
        val result = appViewModel.addSoundItem(uri.toString(), displayName, soundType, modTime)
        if (result != null) {
            Log.w("MainActivity", "Failed to add sound: $result")
        }
    }

    private fun getFileDetailsFromUri(uri: Uri): Pair<String, Long>? {
        return contentResolver.query(uri, arrayOf(
            OpenableColumns.DISPLAY_NAME,
            OpenableColumns.SIZE
        ), null, null, null)?.use { cursor ->
            if (cursor.moveToFirst()) {
                val nameIndex = cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME)
                val sizeIndex = cursor.getColumnIndex(OpenableColumns.SIZE)
                
                val name = if (nameIndex != -1) cursor.getString(nameIndex) else uri.lastPathSegment ?: "Unknown"
                val size = if (sizeIndex != -1) cursor.getLong(sizeIndex) else -1L

                if (size != -1L) {
                    Pair(name, size)
                } else {
                    Pair(name, System.currentTimeMillis())
                }
            } else {
                null
            }
        }
    }

    private fun loadUserFileWithDescriptor(uriString: String, isPlatterSample: Boolean, modTime: Long) {
        try {
            val uri = Uri.parse(uriString)
            contentResolver.openFileDescriptor(uri, "r")?.use { pfd ->
                val statSize = pfd.statSize
                val fd = pfd.detachFd()

                if (isPlatterSample) {
                    loadUserPlatterSample(uriString, fd, 0, statSize, modTime)
                } else {
                    loadUserMusicTrack(uriString, fd, 0, statSize, modTime)
                }
            }
        } catch (e: Exception) {
            Log.e("MainActivity", "Error loading user file: $uriString", e)
        }
    }

    private fun initializeFirebaseMessaging() {
        // Request notification permission for Android 13+
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(
                    this,
                    Manifest.permission.POST_NOTIFICATIONS
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                ActivityCompat.requestPermissions(
                    this,
                    arrayOf(Manifest.permission.POST_NOTIFICATIONS),
                    NOTIFICATION_PERMISSION_REQUEST_CODE
                )
            }
        }
        
        // Get FCM token
        FirebaseMessaging.getInstance().token.addOnCompleteListener { task ->
            if (!task.isSuccessful) {
                Log.w("MainActivity", "Fetching FCM registration token failed", task.exception)
                return@addOnCompleteListener
            }

            // Get new FCM registration token
            val token = task.result
            Log.d("MainActivity", "FCM Registration Token: $token")
            
            // Subscribe to topics for different notification types
            subscribeToTopics()
        }
    }
    
    private fun subscribeToTopics() {
        // Subscribe to update notifications
        FirebaseMessaging.getInstance().subscribeToTopic("updates")
            .addOnCompleteListener { task ->
                var msg = "Subscribed to updates"
                if (!task.isSuccessful) {
                    msg = "Failed to subscribe to updates"
                }
                Log.d("MainActivity", msg)
            }
        
        // Subscribe to promo notifications
        FirebaseMessaging.getInstance().subscribeToTopic("promos")
            .addOnCompleteListener { task ->
                var msg = "Subscribed to promos"
                if (!task.isSuccessful) {
                    msg = "Failed to subscribe to promos"
                }
                Log.d("MainActivity", msg)
            }
        
        // Subscribe to general notifications
        FirebaseMessaging.getInstance().subscribeToTopic("general")
            .addOnCompleteListener { task ->
                var msg = "Subscribed to general notifications"
                if (!task.isSuccessful) {
                    msg = "Failed to subscribe to general notifications"
                }
                Log.d("MainActivity", msg)
            }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            NOTIFICATION_PERMISSION_REQUEST_CODE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    Log.d("MainActivity", "Notification permission granted")
                } else {
                    Log.d("MainActivity", "Notification permission denied")
                }
            }
        }
    }

    /**
     * Test VinylTracker functionality - starts tracking, logs initial angle,
     * then logs angles after delays to verify 120Hz threading is working
     */
    private fun testVinylTracker() {
        Log.i("MainActivity", "=== Testing VinylTracker ===")
        
        try {
            // Start vinyl tracking
            startVinylTracking()
            Log.i("MainActivity", "VinylTracker started successfully")
            
            // Check if tracking is active
            val isTracking = isVinylTracking()
            Log.i("MainActivity", "VinylTracker is tracking: $isTracking")
            
            // Get initial angle
            val initialAngle = getCurrentVinylAngle()
            Log.i("MainActivity", "Initial vinyl angle: $initialAngle degrees")
            
            // Log angles after delays to verify the 120Hz thread is updating
            val handler = Handler(Looper.getMainLooper())
            
            // Log angle after 1 second
            handler.postDelayed({
                val angle1 = getCurrentVinylAngle()
                Log.i("MainActivity", "Vinyl angle after 1 second: $angle1 degrees")
            }, 1000)
            
            // Log angle after 3 seconds
            handler.postDelayed({
                val angle2 = getCurrentVinylAngle()
                Log.i("MainActivity", "Vinyl angle after 3 seconds: $angle2 degrees")
            }, 3000)
            
            // Log angle after 5 seconds
            handler.postDelayed({
                val angle3 = getCurrentVinylAngle()
                Log.i("MainActivity", "Vinyl angle after 5 seconds: $angle3 degrees")
            }, 5000)
            
            // Keep VinylTracker running continuously (removed auto-stop for testing)
            Log.i("MainActivity", "=== VinylTracker Test Complete - Running Continuously ===")

            // Optional: Stop tracking after 60 seconds if needed for testing
            // handler.postDelayed({
            //     stopVinylTracking()
            //     val isStillTracking = isVinylTracking()
            //     Log.i("MainActivity", "VinylTracker stopped. Still tracking: $isStillTracking")
            // }, 60000)
            
        } catch (e: Exception) {
            Log.e("MainActivity", "Error testing VinylTracker", e)
        }
    }

    private fun enableImmersiveMode() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // For Android 11+ (API 30+)
            window.setDecorFitsSystemWindows(false)
            window.insetsController?.let { controller ->
                controller.hide(WindowInsets.Type.systemBars())
                controller.systemBarsBehavior = WindowInsetsController.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
            }
        } else {
            // For older Android versions
            window.decorView.systemUiVisibility = (
                View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                or View.SYSTEM_UI_FLAG_FULLSCREEN
            )
        }
        
        // Keep screen on during DJ sessions
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        
        Log.d("MainActivity", "Immersive mode enabled for precision touch input")
    }

    /**
     * Called from native code when a music track completes
     * This triggers automatic loading of the next track in the playlist
     */
    fun onMusicTrackCompleted() {
        Log.d("MainActivity", "Music track completed - triggering automatic next track")
        appViewModel.onMusicTrackCompleted()
    }

    override fun onResume() {
        super.onResume()
        // Re-enable immersive mode in case system UI reappeared
        enableImmersiveMode()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        // Prevent system dark mode changes from affecting our app
        // Our app maintains its own consistent dark theme
        Log.d("MainActivity", "Configuration changed - maintaining app theme")
    }
}
