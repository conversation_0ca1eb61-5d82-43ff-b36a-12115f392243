# File Loading & Handling System Rebuild Plan

## Goals
- Eliminate inconsistent loading, looping, and file handling issues
- Centralize and simplify all file/URI resolution logic
- Ensure robust error handling and state management
- Make JNI bridge predictable and maintainable
- Document data paths and responsibilities for future maintainers

---

## 1. Architecture Overview
- **Single Entry Point** for all file/sample loading requests
- **Centralized Resolver** for URIs, asset paths, and file paths
- **Unified State Management** for loading, errors, and cache
- **Consistent JNI Bridge** with clear data validation and error propagation
- **Extensible for Future Features** (e.g., new file types, streaming)

---

## 2. Data Path Flow
1. **User Action**: UI triggers a load (file picker, asset select, etc.)
2. **Request Routed** to central loader
3. **Resolver** determines source type:
    - Asset (internal)
    - File (local)
    - Content URI (external)
4. **File/Stream Opened** with error handling
5. **Data Passed** to JNI bridge (as file path or byte array)
6. **Native Layer** processes and returns status/result
7. **State Updated** and UI notified

---

## 3. Key Components
- **FileResolver.kt**: Handles all URI/asset/file resolution
- **SampleLoader.kt**: Central entry point for all load requests
- **LoadingState.kt**: Unified state object for loading/error/success
- **JNIManager.kt**: Handles all native calls, validates inputs, propagates errors
- **CacheManager.kt**: Manages loaded samples/tracks, invalidates on change
- **ErrorHandler.kt**: Centralized error logging and user feedback

---

## 4. Implementation Steps
1. **Design Data Structures** for requests, states, cache
2. **Implement FileResolver** with robust handling for all source types
3. **Build SampleLoader** to route requests and manage state
4. **Refactor JNIManager** for consistent, safe native calls
5. **Integrate CacheManager** to avoid redundant loads and stale data
6. **Centralize Error Handling** for all failures and user feedback
7. **Update UI** to use new loader and state
8. **Write Unit Tests** for all components and edge cases
9. **Document Data Paths** and responsibilities in this .md file

---

## 5. Data Path Examples
- **Asset**: "sounds/sample1" → FileResolver → InputStream → JNIManager
- **User File**: content://... → FileResolver → InputStream → JNIManager
- **Local File**: /storage/... → FileResolver → File → JNIManager

---

## 6. Error Handling
- All errors logged centrally
- User feedback for recoverable errors
- No silent failures or infinite loops

---

## 7. JNI Bridge
- Always validate paths/inputs before native call
- Propagate native errors to state/UI
- Document JNI method signatures and expected data

---

## 8. Extensibility
- New file types: add to FileResolver
- New sample types: add to SampleLoader
- New native features: extend JNIManager

---

## 9. Documentation & Maintenance
- Keep this plan updated as system evolves
- Document all new data paths and error cases
- Add code comments and usage examples

---

## 10. Next Steps
- Review existing code for reusable components
- Begin implementation with FileResolver and SampleLoader
- Refactor incrementally, testing each step
- Update this document as changes are made

---

## Contributors
- GitHub Copilot (AI Agent)
- Future maintainers

---

## Revision History
- 2025-08-23: Initial plan created

---

## Progress Timeline

| Step | Status |
|------|--------|
| 1. Review existing code for reusable components | COMPLETED |
| 2. Design data structures for requests, states, cache | COMPLETED |
| 3. Implement FileResolver with robust handling for all source types | COMPLETED |
| 4. Build SampleLoader to route requests and manage state | COMPLETED |
| 5. Refactor JNIManager for consistent, safe native calls | COMPLETED |
| 6. Integrate CacheManager to avoid redundant loads and stale data | COMPLETED |
| 7. Centralize Error Handling for all failures and user feedback | COMPLETED |
| 8. Update UI to use new loader and state | COMPLETED |
| 9. Write Unit Tests for all components and edge cases | COMPLETED |
| 10. Document data paths and responsibilities in this .md file | IN PROGRESS |

---

## Reusable Components Inventory

The following components are already defined and can be refactored for the new file loading/handling system:

- **AppViewModel**: Central logic and state management for audio loading, tracks, and UI.
- **AudioLoadingState (sealed class)**: Represents loading, idle, and error states for audio operations.
- **SoundType (enum)**: Differentiates between platter samples and music tracks.
- **UserSoundItem (data class)**: Represents user-added sound items with file paths and metadata.
- **TrackManager**: Handles track downloading, saving, and subscription logic.
- **TrialManager**: Manages trial status and related logic.
- **NotificationService & NotificationTestUtils**: For notification handling and testing.
- **TrackReleaseReceiver**: Handles broadcast events for track releases.
- **Various UI Components**: LoadSoundsPage, SoundListItem, TrackListUI, etc.
- **Cache Logic**: Recently loaded tracks, playlist management, and deduplication.
- **Error Handling**: Error states and logging in AppViewModel and TrackManager.
- **Persistence Utilities**: Functions for saving/loading user sound lists and playlists.
- **JNI Bridge Functions**: Native method calls and data passing.

---

## Data Structure Design Plan

### 1. File/Sample Load Request
```kotlin
sealed class FileLoadRequest {
    data class Asset(val assetPath: String) : FileLoadRequest()
    data class LocalFile(val filePath: String) : FileLoadRequest()
    data class ContentUri(val uri: Uri) : FileLoadRequest()
}
```

### 2. File/Sample Load Result
```kotlin
data class FileLoadResult(
    val success: Boolean,
    val data: ByteArray? = null,
    val error: String? = null,
    val sourceType: SourceType,
    val metadata: FileMetadata? = null
)
```

### 3. Source Type Enum
```kotlin
enum class SourceType {
    ASSET, LOCAL_FILE, CONTENT_URI
}
```

### 4. File Metadata
```kotlin
data class FileMetadata(
    val displayName: String?,
    val modTime: Long?,
    val mimeType: String?
)
```

### 5. Loader State
```kotlin
sealed class LoaderState {
    object Idle : LoaderState()
    object Loading : LoaderState()
    data class Success(val result: FileLoadResult) : LoaderState()
    data class Error(val error: String) : LoaderState()
}
```

### 6. Cache Entry
```kotlin
data class CacheEntry(
    val key: String,
    val result: FileLoadResult,
    val timestamp: Long
)
```

### 7. JNI Call Data
```kotlin
data class JniCallData(
    val filePath: String?,
    val buffer: ByteArray?,
    val sourceType: SourceType
)
```

---

- All requests and results are strongly typed for clarity and safety.
- Metadata is attached for UI and cache management.
- LoaderState is unified for UI and error handling.
- CacheEntry supports invalidation and freshness checks.
- JniCallData ensures consistent data passing to native code.

---

## Final Data Path Documentation

### Data Path Flow (Logical)

1. **User Action**: UI triggers a sample/file load (e.g., button press).
2. **SampleLoader**: Receives a FileLoadRequest (Asset, LocalFile, ContentUri).
3. **CacheManager**: Checks for a valid cached result. If found, returns immediately.
4. **FileResolver**: Resolves the request to a ByteArray (for assets/URIs) or file path (for local files).
5. **ErrorHandler**: Logs and maps any errors to user-friendly messages.
6. **JNIManager**: Receives JniCallData and invokes the appropriate native method.
7. **LoaderState**: UI observes LoaderState (Idle, Loading, Success, Error) and updates accordingly.
8. **CacheManager**: Stores successful results for future requests.

### Responsibilities
- **UI**: Triggers loads, observes LoaderState, displays results/errors.
- **SampleLoader**: Orchestrates loading, caching, and state updates.
- **FileResolver**: Handles all file/URI/asset resolution.
- **CacheManager**: Manages cache lifecycle and invalidation.
- **ErrorHandler**: Centralizes error logging and user messaging.
- **JNIManager**: Bridges to native code, validates inputs, propagates errors.

---

## Logical Test Scenarios

- Asset load (valid/invalid path)
- Local file load (valid/invalid path)
- Content URI load (valid/invalid URI)
- Cache hit/miss/expiry
- Error mapping (internal to user message)
- JNI call (buffer vs. file path)

---

## Next: Run Unit Tests
- Validate all logical flows and edge cases using FileLoaderTest.
- Confirm LoaderState transitions and error handling.
- Review logs and user messages for accuracy.

---

Next: Implement FileResolver and SampleLoader using these structures.
