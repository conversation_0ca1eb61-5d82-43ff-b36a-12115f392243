package com.high.ayodj.fileloader

import android.content.Context
import android.net.Uri
import java.io.File

sealed class FileLoadRequest {
    data class Asset(val assetPath: String) : FileLoadRequest()
    data class LocalFile(val filePath: String) : FileLoadRequest()
    data class ContentUri(val uri: Uri) : FileLoadRequest()
}

enum class SourceType {
    ASSET, LOCAL_FILE, CONTENT_URI
}

data class FileMetadata(
    val displayName: String?,
    val modTime: Long?,
    val mimeType: String?
)

data class FileLoadResult(
    val success: Boolean,
    val data: ByteArray? = null,
    val error: String? = null,
    val sourceType: SourceType,
    val metadata: FileMetadata? = null
)

object FileResolver {
    fun resolve(context: Context, request: FileLoadRequest): FileLoadResult {
        return when (request) {
            is FileLoadRequest.Asset -> {
                try {
                    val inputStream = context.assets.open(request.assetPath)
                    val data = inputStream.readBytes()
                    inputStream.close()
                    FileLoadResult(
                        success = true,
                        data = data,
                        sourceType = SourceType.ASSET,
                        metadata = FileMetadata(request.assetPath, null, null)
                    )
                } catch (e: Exception) {
                    FileLoadResult(false, null, e.message, SourceType.ASSET, null)
                }
            }
            is FileLoadRequest.LocalFile -> {
                try {
                    val file = File(request.filePath)
                    val data = file.readBytes()
                    FileLoadResult(
                        success = true,
                        data = data,
                        sourceType = SourceType.LOCAL_FILE,
                        metadata = FileMetadata(file.name, file.lastModified(), null)
                    )
                } catch (e: Exception) {
                    FileLoadResult(false, null, e.message, SourceType.LOCAL_FILE, null)
                }
            }
            is FileLoadRequest.ContentUri -> {
                try {
                    val inputStream = context.contentResolver.openInputStream(request.uri)
                    val data = inputStream?.readBytes()
                    inputStream?.close()
                    FileLoadResult(
                        success = data != null,
                        data = data,
                        sourceType = SourceType.CONTENT_URI,
                        metadata = FileMetadata(request.uri.toString(), null, null)
                    )
                } catch (e: Exception) {
                    FileLoadResult(false, null, e.message, SourceType.CONTENT_URI, null)
                }
            }
        }
    }
}
