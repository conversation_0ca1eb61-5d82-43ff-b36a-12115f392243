graph TD
    subgraph User Input
        A[Touch/Drag on Platter] --> B{isFingerDownOnPlatter_};
        C[Release Platter] --> B;
    end

    subgraph AudioEngine
        B -- true --> D[Finger Control Mode];
        B -- false --> E[Motor Control Mode];

        D --> F[scratchPlatterActiveInternal];
        F -- angleDelta --> G[platterTargetPlaybackRate_];
        G -- direct rate --> H[AudioSample::getAudio];

        E --> I[SlipmatPhysics::calculateNextSpeed];
        I -- physics rate --> G;

        C --> J[releasePlatterTouchInternal];
        J --> K[Sync MasterTickSystem to Audio Position];

        subgraph Oboe Audio Callback
            L[onAudioReady] --> M{Control Mode?};
            M -- Finger --> N[Advance Audio by platterTargetPlaybackRate_];
            M -- Motor --> O[Set Audio Position from MasterTickSystem];
            N --> P[Mix Audio];
            O --> P;
        end
    end

    subgraph Timing & Sync
        Q[MasterTickSystem] -- master tick --> L;
        Q -- ticksToVinylAngle --> R[VinylTracker];
        K -- reset/sync --> Q;
    end

    subgraph Visuals
        R -- currentAngle_ --> S[UI Thread];
        S -- 60fps --> T[Render Vinyl Rotation];
    end

    style User Input fill:#f9f,stroke:#333,stroke-width:2px
    style AudioEngine fill:#ccf,stroke:#333,stroke-width:2px
    style Timing & Sync fill:#cfc,stroke:#333,stroke-width:2px
    style Visuals fill:#fcf,stroke:#333,stroke-width:2px
```

### Explanation of the Diagram

This diagram illustrates the two main modes of operation for the vinyl physics system: **Finger Control** and **Motor Control**.

*   **User Input:** The system starts with the user's interaction with the platter. The `isFingerDownOnPlatter_` flag is the central switch that determines the current mode.

*   **AudioEngine (Control Flow):**
    *   **Finger Control Mode:** When the user's finger is on the platter, `scratchPlatterActiveInternal` is called with the angle delta from the UI. This directly sets the `platterTargetPlaybackRate_`, which is then used in the `AudioSample::getAudio` method to advance the audio position. This ensures immediate, low-latency response to the user's scratching.
    *   **Motor Control Mode:** When the user's finger is off the platter, the `SlipmatPhysics` engine takes over. It calculates the next speed based on its physics parameters (damping, abruptness, etc.) to smoothly return the platter to its target speed. This calculated rate is then used to set the `platterTargetPlaybackRate_`.
    *   **The Seamless Transition:** The most critical part is the `releasePlatterTouchInternal` function. When the user releases the platter, it calls `Sync MasterTickSystem to Audio Position`. This is the "seamless" part of the hybrid sync, ensuring that the `MasterTickSystem` is perfectly aligned with the audio's current position at the moment of release, preventing any audible skips.

*   **Oboe Audio Callback (Data Flow):**
    *   The `onAudioReady` callback is the heart of the audio processing. It checks the control mode.
    *   In **Finger Control** mode, it advances the audio based on the `platterTargetPlaybackRate_`.
    *   In **Motor Control** mode, it sets the audio position directly from the `MasterTickSystem`'s current tick, ensuring perfect synchronization with the motor speed.

*   **Timing & Sync:**
    *   The `MasterTickSystem` is the "single source of truth" for timing. It's incremented by the audio callback.
    *   The `VinylTracker` runs in a separate thread and gets the current vinyl angle from the `MasterTickSystem`.
    *   The `MasterTickSystem` is reset and synchronized by the `releasePlatterTouchInternal` function.

*   **Visuals:**
    *   The `VinylTracker` provides the current angle to the UI thread, which then renders the vinyl's rotation on the screen at 60fps.

This hybrid approach provides the best of both worlds: the immediate response of direct finger control and the perfect stability of a tick-based motor system, with a seamless transition between the two.
