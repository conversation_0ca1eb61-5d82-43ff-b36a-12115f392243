package com.high.ayodj.monetization

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.util.Log
import java.util.*

/**
 * Handles scheduling of track release notifications and weekend reminders
 * 
 * IMPORTANT: This class now uses INEXACT alarms instead of exact alarms to comply
 * with app store policies. The notifications will still be delivered, but may be
 * delayed by the system for battery optimization.
 * 
 * Alternative approaches for more precise timing:
 * 1. WorkManager - For background tasks (add androidx.work dependency)
 * 2. Firebase Cloud Messaging - For server-triggered notifications
 * 3. Foreground Service - For time-critical operations (requires user permission)
 */
class NotificationScheduler(private val context: Context) {
    
    private val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
    
    companion object {
        private const val TAG = "NotificationScheduler"
        private const val RELEASE_HOUR = 16 // 4 PM
        private const val RELEASE_MINUTE = 20 // 4:20 PM
        private const val THURSDAY = Calendar.THURSDAY
        private const val WEEKEND_REMINDER_HOUR = 12 // Noon
        
        // Notification message pool - easily configurable
        val NOTIFICATION_MESSAGES = arrayOf(
            "New track dropped! 🎵",
            "You cooking huh? Exclusive records!",
            "Freshhh presss!",
            "Weekly heat incoming 🔥",
            "Fresh cuts ready! 🎧",
            "Beat drop Thursday! 🔥",
            "AyoDJ exclusive just landed! 🎶",
            "Thursday vibes activated! 🔊",
            "New heat for the decks! 🎚️",
            "Weekly fire just dropped! 🔥"
        )
        
        val WEEKEND_REMINDER_MESSAGES = arrayOf(
            "Don't miss this week's fresh tracks! 🎧",
            "Weekend vibes with new beats! 🎶",
            "Still time to catch the weekly drop! ⏰",
            "Fresh tracks waiting for you! 🎵",
            "Weekend sessions just got better! 🔊"
        )
    }
    
    /**
     * Schedule notifications for all tracks in the list
     * @param useWorkManager if true, uses WorkManager instead of AlarmManager for better battery optimization
     */
    fun scheduleWeeklyReleases(tracks: List<Track>, useWorkManager: Boolean = false) {
        Log.i(TAG, "Scheduling notifications for ${tracks.size} tracks using ${if (useWorkManager) "WorkManager" else "AlarmManager"}")
        
        tracks.forEach { track ->
            if (!track.notificationSent) {
                if (useWorkManager) {
                    scheduleTrackReleaseWithWorkManager(track)
                } else {
                    scheduleTrackRelease(track)
                }
            }
        }
    }
    
    /**
     * Schedule notification for a specific track release using WorkManager
     */
    private fun scheduleTrackReleaseWithWorkManager(track: Track) {
        val releaseTime = calculateReleaseTime(track.releaseTimestamp)
        val currentTime = System.currentTimeMillis()
        
        // Don't schedule notifications for tracks that should have already been released
        if (releaseTime <= currentTime) {
            Log.d(TAG, "Skipping WorkManager notification for past track: ${track.id}")
            return
        }
        
        val delayMillis = releaseTime - currentTime
        val message = getRandomNotificationMessage()
        
        NotificationWorker.scheduleNotification(
            context = context,
            trackId = track.id,
            trackTitle = track.title,
            trackArtist = track.artist,
            message = message,
            delayMillis = delayMillis
        )
    }

    /**
     * Schedule notification for a specific track release
     */
    private fun scheduleTrackRelease(track: Track) {
        val releaseTime = calculateReleaseTime(track.releaseTimestamp)
        
        // Don't schedule notifications for tracks that should have already been released
        if (releaseTime <= System.currentTimeMillis()) {
            Log.d(TAG, "Skipping notification for past track: ${track.id}")
            return
        }
        
        val intent = Intent(context, TrackReleaseReceiver::class.java).apply {
            putExtra("track_id", track.id)
            putExtra("track_title", track.title)
            putExtra("track_artist", track.artist)
            putExtra("notification_message", getRandomNotificationMessage())
            putExtra("is_weekend_reminder", false)
        }
        
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            track.id.hashCode(),
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        try {
            // Use inexact alarm for better battery optimization
            // This will trigger within a reasonable window of the target time
            alarmManager.set(
                AlarmManager.RTC_WAKEUP,
                releaseTime,
                pendingIntent
            )
            Log.i(TAG, "Scheduled notification for track ${track.id} at ${Date(releaseTime)} (inexact timing)")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to schedule notification for track ${track.id}", e)
        }
    }
    
    /**
     * Schedule weekend reminder notifications
     */
    fun scheduleWeekendReminder() {
        val calendar = Calendar.getInstance().apply {
            set(Calendar.DAY_OF_WEEK, Calendar.SATURDAY)
            set(Calendar.HOUR_OF_DAY, WEEKEND_REMINDER_HOUR)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
            
            // If it's already past Saturday noon, schedule for next week
            if (before(Calendar.getInstance())) {
                add(Calendar.WEEK_OF_YEAR, 1)
            }
        }
        
        val intent = Intent(context, TrackReleaseReceiver::class.java).apply {
            putExtra("is_weekend_reminder", true)
            putExtra("notification_message", getRandomWeekendMessage())
        }
        
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            "weekend_reminder".hashCode(),
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        try {
            alarmManager.setRepeating(
                AlarmManager.RTC_WAKEUP,
                calendar.timeInMillis,
                AlarmManager.INTERVAL_DAY * 7, // Weekly
                pendingIntent
            )
            Log.i(TAG, "Scheduled weekend reminders starting ${Date(calendar.timeInMillis)}")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to schedule weekend reminders", e)
        }
    }
    
    /**
     * Calculate the next Thursday 4:20 PM release time
     */
    fun calculateNextThursday420PM(): Long {
        val calendar = Calendar.getInstance().apply {
            set(Calendar.DAY_OF_WEEK, THURSDAY)
            set(Calendar.HOUR_OF_DAY, RELEASE_HOUR)
            set(Calendar.MINUTE, RELEASE_MINUTE)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
            
            // If it's already past Thursday 4:20 PM, schedule for next week
            if (before(Calendar.getInstance())) {
                add(Calendar.WEEK_OF_YEAR, 1)
            }
        }
        return calendar.timeInMillis
    }
    
    /**
     * Calculate release time for a track (Thursday 4:20 PM of the track's week)
     */
    private fun calculateReleaseTime(trackTimestamp: Long): Long {
        val calendar = Calendar.getInstance().apply {
            timeInMillis = trackTimestamp
            set(Calendar.DAY_OF_WEEK, THURSDAY)
            set(Calendar.HOUR_OF_DAY, RELEASE_HOUR)
            set(Calendar.MINUTE, RELEASE_MINUTE)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }
        return calendar.timeInMillis
    }
    
    /**
     * Cancel all pending notifications
     */
    fun cancelAllNotifications() {
        // Cancel weekend reminders
        val weekendIntent = Intent(context, TrackReleaseReceiver::class.java)
        val weekendPendingIntent = PendingIntent.getBroadcast(
            context,
            "weekend_reminder".hashCode(),
            weekendIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        alarmManager.cancel(weekendPendingIntent)
        
        Log.i(TAG, "Cancelled all notifications")
    }
    
    /**
     * Cancel notification for a specific track
     * @param trackId the track ID to cancel
     * @param useWorkManager if true, cancels WorkManager tasks instead of AlarmManager
     */
    fun cancelTrackNotification(trackId: String, useWorkManager: Boolean = false) {
        if (useWorkManager) {
            NotificationWorker.cancelNotification(context, trackId)
        } else {
            val intent = Intent(context, TrackReleaseReceiver::class.java)
            val pendingIntent = PendingIntent.getBroadcast(
                context,
                trackId.hashCode(),
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            alarmManager.cancel(pendingIntent)
            Log.d(TAG, "Cancelled AlarmManager notification for track: $trackId")
        }
    }
    
    /**
     * Get a random notification message
     */
    private fun getRandomNotificationMessage(): String {
        return NOTIFICATION_MESSAGES.random()
    }
    
    /**
     * Get a random weekend reminder message
     */
    private fun getRandomWeekendMessage(): String {
        return WEEKEND_REMINDER_MESSAGES.random()
    }
    
    /**
     * Create daily release schedule for beta testing
     */
    fun scheduleDailyReleasesForTesting(tracks: List<Track>) {
        Log.i(TAG, "Scheduling DAILY releases for beta testing")
        
        tracks.forEachIndexed { index, track ->
            val calendar = Calendar.getInstance().apply {
                add(Calendar.DAY_OF_YEAR, index) // One track per day
                set(Calendar.HOUR_OF_DAY, RELEASE_HOUR)
                set(Calendar.MINUTE, RELEASE_MINUTE)
                set(Calendar.SECOND, 0)
                set(Calendar.MILLISECOND, 0)
            }
            
            val intent = Intent(context, TrackReleaseReceiver::class.java).apply {
                putExtra("track_id", track.id)
                putExtra("track_title", track.title)
                putExtra("track_artist", track.artist)
                putExtra("notification_message", getRandomNotificationMessage())
                putExtra("is_beta_test", true)
            }
            
            val pendingIntent = PendingIntent.getBroadcast(
                context,
                "beta_${track.id}".hashCode(),
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            
            try {
                // Use inexact alarm for better battery optimization
                alarmManager.set(
                    AlarmManager.RTC_WAKEUP,
                    calendar.timeInMillis,
                    pendingIntent
                )
                Log.i(TAG, "Scheduled BETA notification for track ${track.id} at ${Date(calendar.timeInMillis)} (inexact timing)")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to schedule BETA notification for track ${track.id}", e)
            }
        }
    }
}

/**
 * Utility class for notification message management
 */
object NotificationMessages {
    
    /**
     * Add a new notification message to the pool
     */
    fun addCustomMessage(message: String) {
        // In a real implementation, this would update a shared preferences or database
        // For now, we'll just log it
        Log.i("NotificationMessages", "Custom message added: $message")
    }
    
    /**
     * Get all available notification messages
     */
    fun getAllMessages(): Array<String> {
        return NotificationScheduler.NOTIFICATION_MESSAGES
    }
    
    /**
     * Get all weekend reminder messages
     */
    fun getAllWeekendMessages(): Array<String> {
        return NotificationScheduler.WEEKEND_REMINDER_MESSAGES
    }
}
