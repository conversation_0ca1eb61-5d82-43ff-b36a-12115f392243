package com.high.ayodj.monetization

import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.work.CoroutineWorker
import androidx.work.Data
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import java.util.concurrent.TimeUnit

/**
 * WorkManager-based notification worker as an alternative to AlarmManager
 * This provides better battery optimization and respects system doze modes
 */
class NotificationWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {

    companion object {
        private const val TAG = "NotificationWorker"
        private const val KEY_TRACK_ID = "track_id"
        private const val KEY_TRACK_TITLE = "track_title"
        private const val KEY_TRACK_ARTIST = "track_artist"
        private const val KEY_NOTIFICATION_MESSAGE = "notification_message"
        private const val KEY_IS_WEEKEND_REMINDER = "is_weekend_reminder"
        
        /**
         * Schedule a notification using WorkManager
         */
        fun scheduleNotification(
            context: Context,
            trackId: String,
            trackTitle: String,
            trackArtist: String,
            message: String,
            delayMillis: Long,
            isWeekendReminder: Boolean = false
        ) {
            val inputData = Data.Builder()
                .putString(KEY_TRACK_ID, trackId)
                .putString(KEY_TRACK_TITLE, trackTitle)
                .putString(KEY_TRACK_ARTIST, trackArtist)
                .putString(KEY_NOTIFICATION_MESSAGE, message)
                .putBoolean(KEY_IS_WEEKEND_REMINDER, isWeekendReminder)
                .build()

            val workRequest = OneTimeWorkRequestBuilder<NotificationWorker>()
                .setInputData(inputData)
                .setInitialDelay(delayMillis, TimeUnit.MILLISECONDS)
                .addTag("track_notification_$trackId")
                .build()

            WorkManager.getInstance(context).enqueue(workRequest)
            Log.i(TAG, "Scheduled WorkManager notification for track $trackId with delay ${delayMillis}ms")
        }
        
        /**
         * Cancel a scheduled notification
         */
        fun cancelNotification(context: Context, trackId: String) {
            WorkManager.getInstance(context).cancelAllWorkByTag("track_notification_$trackId")
            Log.d(TAG, "Cancelled WorkManager notification for track: $trackId")
        }
    }

    override suspend fun doWork(): Result {
        return try {
            val trackId = inputData.getString(KEY_TRACK_ID) ?: return Result.failure()
            val trackTitle = inputData.getString(KEY_TRACK_TITLE) ?: ""
            val trackArtist = inputData.getString(KEY_TRACK_ARTIST) ?: ""
            val message = inputData.getString(KEY_NOTIFICATION_MESSAGE) ?: "New track available!"
            val isWeekendReminder = inputData.getBoolean(KEY_IS_WEEKEND_REMINDER, false)

            // Send broadcast to TrackReleaseReceiver to trigger notification
            val intent = Intent(applicationContext, TrackReleaseReceiver::class.java).apply {
                putExtra("track_id", trackId)
                putExtra("track_title", trackTitle)
                putExtra("track_artist", trackArtist)
                putExtra("notification_message", message)
                putExtra("is_weekend_reminder", isWeekendReminder)
                putExtra("triggered_by_workmanager", true)
            }
            
            applicationContext.sendBroadcast(intent)
            Log.i(TAG, "Triggered notification for track $trackId via WorkManager")
            
            Result.success()
        } catch (e: Exception) {
            Log.e(TAG, "Failed to process notification work", e)
            Result.failure()
        }
    }
}
