package com.high.ayodj.monetization

import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.IBinder
import android.util.Log
import kotlinx.coroutines.*
import java.util.*

/**
 * Service that handles monthly pack downloads and track scheduling
 */
class MonthlyUpdateService : Service() {
    
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private lateinit var trackManager: TrackManager
    private lateinit var notificationScheduler: NotificationScheduler
    
    companion object {
        private const val TAG = "MonthlyUpdateService"
        private const val ACTION_CHECK_MONTHLY_UPDATE = "com.high.ayodj.CHECK_MONTHLY_UPDATE"
        private const val ACTION_SCHEDULE_NOTIFICATIONS = "com.high.ayodj.SCHEDULE_NOTIFICATIONS"
        
        fun startMonthlyCheck(context: Context) {
            val intent = Intent(context, MonthlyUpdateService::class.java).apply {
                action = ACTION_CHECK_MONTHLY_UPDATE
            }
            context.startService(intent)
        }
        
        fun scheduleNotifications(context: Context) {
            val intent = Intent(context, MonthlyUpdateService::class.java).apply {
                action = ACTION_SCHEDULE_NOTIFICATIONS
            }
            context.startService(intent)
        }
    }
    
    override fun onCreate() {
        super.onCreate()
        trackManager = TrackManager(this)
        notificationScheduler = NotificationScheduler(this)
        Log.i(TAG, "MonthlyUpdateService created")
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        val action = intent?.action
        
        Log.i(TAG, "Service started with action: $action")
        
        when (action) {
            ACTION_CHECK_MONTHLY_UPDATE -> {
                serviceScope.launch {
                    checkForMonthlyUpdate()
                    stopSelf(startId)
                }
            }
            ACTION_SCHEDULE_NOTIFICATIONS -> {
                serviceScope.launch {
                    scheduleAllNotifications()
                    stopSelf(startId)
                }
            }
            else -> {
                Log.w(TAG, "Unknown action: $action")
                stopSelf(startId)
            }
        }
        
        return START_NOT_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onDestroy() {
        serviceScope.cancel()
        Log.i(TAG, "MonthlyUpdateService destroyed")
        super.onDestroy()
    }
    
    /**
     * Check if we need to download new monthly content
     */
    private suspend fun checkForMonthlyUpdate() {
        try {
            Log.i(TAG, "Checking for monthly updates...")
            
            val lastUpdateCheck = getLastUpdateCheckTime()
            val currentTime = System.currentTimeMillis()
            val calendar = Calendar.getInstance()
            
            // Check if we've already updated this month
            val lastUpdateCalendar = Calendar.getInstance().apply {
                timeInMillis = lastUpdateCheck
            }
            
            val shouldUpdate = lastUpdateCalendar.get(Calendar.MONTH) != calendar.get(Calendar.MONTH) ||
                    lastUpdateCalendar.get(Calendar.YEAR) != calendar.get(Calendar.YEAR) ||
                    lastUpdateCheck == 0L
            
            if (shouldUpdate) {
                Log.i(TAG, "Monthly update required")
                downloadMonthlyPack()
                setLastUpdateCheckTime(currentTime)
            } else {
                Log.i(TAG, "Monthly update not needed")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error during monthly update check", e)
        }
    }
    
    /**
     * Download the monthly pack of tracks
     */
    private suspend fun downloadMonthlyPack() {
        try {
            Log.i(TAG, "Starting monthly pack download...")
            
            // In a real implementation, this would:
            // 1. Check subscription status
            // 2. Download from server/CDN
            // 3. Validate downloaded files
            // 4. Update local database
            
            // For now, we'll create mock tracks
            val mockTracks = createMonthlyMockTracks()
            
            // Add tracks to database
            trackManager.addTracks(mockTracks)
            
            // Schedule notifications for new tracks
            scheduleNotificationsForTracks(mockTracks)
            
            Log.i(TAG, "Monthly pack download completed: ${mockTracks.size} tracks")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error downloading monthly pack", e)
            // TODO: Implement retry logic
        }
    }
    
    /**
     * Create mock tracks for the current month
     */
    private fun createMonthlyMockTracks(): List<Track> {
        val calendar = Calendar.getInstance()
        val currentMonth = calendar.get(Calendar.MONTH) + 1
        val currentYear = calendar.get(Calendar.YEAR)
        val tracks = mutableListOf<Track>()
        
        // Create 4-5 tracks for the current month (one per week)
        val weeksInMonth = calendar.getActualMaximum(Calendar.WEEK_OF_MONTH)
        
        for (week in 1..minOf(weeksInMonth, 5)) {
            // Calculate release date (Thursday of each week)
            val releaseCalendar = Calendar.getInstance().apply {
                set(Calendar.YEAR, currentYear)
                set(Calendar.MONTH, calendar.get(Calendar.MONTH))
                set(Calendar.WEEK_OF_MONTH, week)
                set(Calendar.DAY_OF_WEEK, Calendar.THURSDAY)
                set(Calendar.HOUR_OF_DAY, 16) // 4 PM
                set(Calendar.MINUTE, 20) // 4:20 PM
                set(Calendar.SECOND, 0)
                set(Calendar.MILLISECOND, 0)
            }
            
            // Create 'a' track
            val trackA = Track(
                id = "track_${currentYear}_b${currentMonth}_w${week}_a",
                filename = "batch_${currentMonth.toString().padStart(3, '0')}-${week.toString().padStart(2, '0')}-${currentYear.toString().takeLast(2)}a.mp3",
                title = "AyoDJ Batch Drop #${currentMonth}.${week}A",
                artist = "AyoDJ Collective",
                batch = currentMonth,
                week = week,
                year = currentYear,
                variant = "a",
                releaseTimestamp = releaseCalendar.timeInMillis,
                isUnlocked = false, // Will be unlocked by notification
                isPremiumOnly = false,
                notificationSent = false,
                downloadUrl = "https://ayodj.com/releases/${currentYear}/${currentMonth}/week${week}_a.mp3",
                fileSizeBytes = (6_000_000L..12_000_000L).random(), // 6-12MB
                durationSeconds = (180..300).random() // 3-5 minutes
            )
            tracks.add(trackA)
            
            // Create 'b' track
            val trackB = Track(
                id = "track_${currentYear}_b${currentMonth}_w${week}_b",
                filename = "batch_${currentMonth.toString().padStart(3, '0')}-${week.toString().padStart(2, '0')}-${currentYear.toString().takeLast(2)}b.mp3",
                title = "AyoDJ Batch Drop #${currentMonth}.${week}B",
                artist = "AyoDJ Collective",
                batch = currentMonth,
                week = week,
                year = currentYear,
                variant = "b",
                releaseTimestamp = releaseCalendar.timeInMillis + 1000, // Slightly later timestamp
                isUnlocked = false, // Will be unlocked by notification
                isPremiumOnly = false,
                notificationSent = false,
                downloadUrl = "https://ayodj.com/releases/${currentYear}/${currentMonth}/week${week}_b.mp3",
                fileSizeBytes = (6_000_000L..12_000_000L).random(), // 6-12MB
                durationSeconds = (180..300).random() // 3-5 minutes
            )
            tracks.add(trackB)
        }
        
        return tracks
    }
    
    /**
     * Schedule all notifications for the app
     */
    private suspend fun scheduleAllNotifications() {
        try {
            Log.i(TAG, "Scheduling all notifications...")
            
            val tracks = trackManager.getVisibleTracks()
            val unlockedTracks = tracks.filter { !it.isUnlocked }
            
            // Schedule track release notifications
            notificationScheduler.scheduleWeeklyReleases(unlockedTracks)
            
            // Schedule weekend reminders
            notificationScheduler.scheduleWeekendReminder()
            
            Log.i(TAG, "Scheduled notifications for ${unlockedTracks.size} tracks")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error scheduling notifications", e)
        }
    }
    
    /**
     * Schedule notifications for specific tracks
     */
    private fun scheduleNotificationsForTracks(tracks: List<Track>) {
        val unlockedTracks = tracks.filter { !it.isUnlocked }
        notificationScheduler.scheduleWeeklyReleases(unlockedTracks)
        Log.i(TAG, "Scheduled notifications for ${unlockedTracks.size} new tracks")
    }
    
    /**
     * Get the last update check timestamp
     */
    private fun getLastUpdateCheckTime(): Long {
        val prefs = getSharedPreferences("monthly_update", Context.MODE_PRIVATE)
        return prefs.getLong("last_update_check", 0L)
    }
    
    /**
     * Set the last update check timestamp
     */
    private fun setLastUpdateCheckTime(time: Long) {
        val prefs = getSharedPreferences("monthly_update", Context.MODE_PRIVATE)
        prefs.edit().putLong("last_update_check", time).apply()
    }
}

/**
 * Helper class for managing monthly updates
 */
object MonthlyUpdateManager {
    
    /**
     * Check if monthly update is needed
     */
    fun isMonthlyUpdateNeeded(context: Context): Boolean {
        val prefs = context.getSharedPreferences("monthly_update", Context.MODE_PRIVATE)
        val lastCheck = prefs.getLong("last_update_check", 0L)
        
        if (lastCheck == 0L) return true
        
        val lastCheckCalendar = Calendar.getInstance().apply { timeInMillis = lastCheck }
        val currentCalendar = Calendar.getInstance()
        
        return lastCheckCalendar.get(Calendar.MONTH) != currentCalendar.get(Calendar.MONTH) ||
                lastCheckCalendar.get(Calendar.YEAR) != currentCalendar.get(Calendar.YEAR)
    }
    
    /**
     * Force a monthly update check
     */
    fun forceMonthlyUpdate(context: Context) {
        MonthlyUpdateService.startMonthlyCheck(context)
    }
    
    /**
     * Initialize the system for first run
     */
    fun initializeFirstRun(context: Context) {
        val prefs = context.getSharedPreferences("monthly_update", Context.MODE_PRIVATE)
        val hasInitialized = prefs.getBoolean("has_initialized", false)
        
        if (!hasInitialized) {
            Log.i("MonthlyUpdateManager", "First run initialization")
            
            // Create initial tracks
            val trackManager = TrackManager(context)
            val mockTracks = trackManager.createTracksFromAssets()
            trackManager.addTracks(mockTracks)
            
            // Schedule notifications
            MonthlyUpdateService.scheduleNotifications(context)
            
            // Mark as initialized
            prefs.edit().putBoolean("has_initialized", true).apply()
            
            Log.i("MonthlyUpdateManager", "First run initialization completed")
        }
    }
}
