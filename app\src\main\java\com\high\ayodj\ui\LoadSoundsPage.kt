package com.high.ayodj.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material3.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.Star
import androidx.compose.material.icons.filled.Person
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.high.ayodj.UserSoundItem
import com.high.ayodj.TrackSource

@Composable
fun LoadSoundsPage(
    // pageTitle: String, // Removed, as TopAppBar is removed
    soundList: List<UserSoundItem>,
    onAddItemClick: () -> Unit,
    onToggleSave: (index: Int) -> Unit,
    onLoadClick: () -> Unit,
    onCancelClick: () -> Unit,
    onSettingsClick: () -> Unit,
    onTrackListClick: () -> Unit,
    maxItems: Int,
    listFullErrorMessage: String?,
    subscriptionStatus: com.high.ayodj.monetization.SubscriptionStatus
) {
    val userItemCount = soundList.count { !it.isHardcoded }
    val canAddMore = (userItemCount < maxItems || soundList.any { !it.isHardcoded && !it.saveToAutoLoad }) &&
                     subscriptionStatus != com.high.ayodj.monetization.SubscriptionStatus.FREE

    Column(
    modifier = Modifier
        .fillMaxSize()
        .background(Color(0xFF1A1A1A))
            .padding(WindowInsets.statusBars.asPaddingValues()) // Respect status bar
            .padding(horizontal = 16.dp) // Horizontal padding for content area
            // Adjusted vertical padding to bring content up slightly
            .padding(top = 8.dp, bottom = 16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth(0.9f)
                .padding(bottom = 16.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            Button(
                onClick = onAddItemClick,
                enabled = canAddMore,
                colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.primary),
                modifier = Modifier.weight(1f).padding(end = 8.dp)
            ) {
                Icon(Icons.Filled.Add, contentDescription = "Add Sound", tint = Color.White)
                Spacer(Modifier.width(4.dp))
                Text("Add Sound", color = Color.White, fontSize = 12.sp)
            }
            
            Button(
                onClick = onSettingsClick,
                colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.secondary),
                modifier = Modifier.weight(1f).padding(start = 8.dp)
            ) {
                Icon(Icons.Default.Settings, contentDescription = "Settings", tint = Color.White)
                Spacer(Modifier.width(4.dp))
                Text("Settings", color = Color.White, fontSize = 12.sp)
            }
        }

        if (!listFullErrorMessage.isNullOrEmpty()) {
            Text(
                text = listFullErrorMessage,
                color = MaterialTheme.colorScheme.error,
                modifier = Modifier.padding(vertical = 8.dp)
            )
        } else if (!canAddMore) {
            Text(
                text = "List is full. Unselect 'save' on an item to replace it.",
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(vertical = 8.dp)
            )
        }

        if (soundList.isEmpty()) {
            Box(modifier = Modifier.fillMaxSize().weight(1f), contentAlignment = Alignment.Center) {
                Text("No sounds loaded yet. Click 'Add Sound' to begin.", color = Color.White)
            }
        } else {
            LazyColumn(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth()
                    .background(Color(0xFF2C2C2C), shape = MaterialTheme.shapes.medium)
                    .padding(8.dp)
            ) {
                val userItems = soundList.filter { !it.isHardcoded }.sortedBy { it.displayName }
                val hardcodedItems = soundList.filter { it.isHardcoded }.sortedBy { it.displayName }

                if (userItems.isNotEmpty()) {
                    item {
                        Text(
                            "Ayo Cuts!",
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color.White,
                            modifier = Modifier.padding(vertical = 8.dp)
                        )
                    }
                    itemsIndexed(userItems) { _, item ->
                        val originalIndex = soundList.indexOf(item)
                        SoundListItem(
                            item = item,
                            onToggleSave = { onToggleSave(originalIndex) },
                            subscriptionStatus = subscriptionStatus
                        )
                        if (soundList.indexOf(item) < soundList.size - 1) {
                            HorizontalDivider(color = Color.Gray.copy(alpha = 0.5f))
                        }
                    }
                }

                if (hardcodedItems.isNotEmpty()) {
                    item {
                        Text(
                            "Ayo Feats!",
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color.White,
                            modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
                        )
                    }
                    itemsIndexed(hardcodedItems) { _, item ->
                        val originalIndex = soundList.indexOf(item)
                        SoundListItem(
                            item = item,
                            onToggleSave = { onToggleSave(originalIndex) },
                            subscriptionStatus = subscriptionStatus
                        )
                    }
                }
            }
        }

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 16.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            Button(
                onClick = onCancelClick,
                colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF555555)),
                modifier = Modifier.weight(1f).padding(end = 4.dp)
            ) {
                Text("Cancel", color = Color.White)
            }
            
            Button(
                onClick = onTrackListClick,
                colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.primary),
                modifier = Modifier.weight(1f).padding(horizontal = 4.dp)
            ) {
                Text("Tracks", color = Color.White, fontSize = 12.sp)
            }
            
            Button(
                onClick = onLoadClick,
                colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.secondary),
                modifier = Modifier.weight(1f).padding(start = 4.dp)
            ) {
                Text("Load", color = Color.White)
            }
        }
    }
}

@Composable
fun SoundListItem(
    item: UserSoundItem,
    onToggleSave: () -> Unit,
    subscriptionStatus: com.high.ayodj.monetization.SubscriptionStatus
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(6.dp)
                ) {
                    Text(
                        text = item.title ?: item.displayName,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        maxLines = 1,
                        overflow = androidx.compose.ui.text.style.TextOverflow.Ellipsis,
                        modifier = Modifier.weight(1f, fill = false)
                    )
                    
                    // Source indicator icon
                    when (item.source) {
                        TrackSource.AYO_OFFICIAL -> {
                            Icon(
                                imageVector = Icons.Default.Star,
                                contentDescription = "Ayo Official",
                                tint = Color(0xFFFFD700), // Gold color for official tracks
                                modifier = Modifier.size(16.dp)
                            )
                        }
                        TrackSource.USER_ADDED -> {
                            Icon(
                                imageVector = Icons.Default.Person,
                                contentDescription = "User Added",
                                tint = Color(0xFF4CAF50), // Green color for user tracks
                                modifier = Modifier.size(16.dp)
                            )
                        }
                    }
                }
                
                if (!item.artist.isNullOrEmpty()) {
                    Text(
                        text = item.artist,
                        fontSize = 12.sp,
                        color = Color.Gray,
                        maxLines = 1,
                        overflow = androidx.compose.ui.text.style.TextOverflow.Ellipsis
                    )
                }
                if (item.durationSeconds > 0) {
                    Text(
                        text = formatDuration(item.durationSeconds),
                        fontSize = 11.sp,
                        color = Color.Gray
                    )
                }
            }
            Spacer(modifier = Modifier.width(8.dp))
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                Switch(
                    checked = item.saveToAutoLoad,
                    onCheckedChange = { onToggleSave() },
                    enabled = when (subscriptionStatus) {
                        com.high.ayodj.monetization.SubscriptionStatus.FREE -> false // FREE users cannot modify URI list
                        com.high.ayodj.monetization.SubscriptionStatus.FULL_PREMIUM -> true // Premium can toggle everything
                        else -> !item.isHardcoded // Other tiers can toggle non-hardcoded items only
                    }
                )
            }
        }
    }
}

/**
 * Helper function to format duration from seconds to MM:SS format
 */
private fun formatDuration(durationSeconds: Int): String {
    val minutes = durationSeconds / 60
    val seconds = durationSeconds % 60
    return "%d:%02d".format(minutes, seconds)
}
