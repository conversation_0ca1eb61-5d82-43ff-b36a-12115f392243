package com.high.ayodj

import android.util.Log
import com.google.firebase.messaging.FirebaseMessaging
import com.google.firebase.messaging.RemoteMessage

/**
 * Test utility for Firebase notifications
 * This class provides methods to test different types of notifications
 */
object NotificationTestUtils {
    
    private const val TAG = "NotificationTestUtils"
    
    /**
     * Test method to simulate receiving different types of notifications
     * In production, these would come from your server
     */
    fun testUpdateNotification() {
        Log.d(TAG, "Testing update notification")
        
        // Create a test remote message for update notification
        val data = mapOf(
            "type" to "update",
            "title" to "AyoDJ Update Available!",
            "body" to "Version 2.0 is now available with new scratch effects and improved performance!"
        )
        
        // In a real scenario, this would be handled by the messaging service
        // For testing, we'll just log the data
        Log.d(TAG, "Update notification data: $data")
    }
    
    fun testPromoNotification() {
        Log.d(TAG, "Testing promo notification")
        
        val data = mapOf(
            "type" to "promo",
            "title" to "Special Offer - 50% Off Premium!",
            "body" to "Unlock all premium features for half price. Limited time offer!"
        )
        
        Log.d(TAG, "Promo notification data: $data")
    }
    
    fun testGeneralNotification() {
        Log.d(TAG, "Testing general notification")
        
        val data = mapOf(
            "type" to "general",
            "title" to "AyoDJ Community",
            "body" to "Check out the latest beats shared by the community!"
        )
        
        Log.d(TAG, "General notification data: $data")
    }
    
    /**
     * Get the current FCM token for testing
     */
    fun logCurrentToken() {
        FirebaseMessaging.getInstance().token.addOnCompleteListener { task ->
            if (!task.isSuccessful) {
                Log.w(TAG, "Fetching FCM registration token failed", task.exception)
                return@addOnCompleteListener
            }
            
            val token = task.result
            Log.d(TAG, "Current FCM Token: $token")
            Log.d(TAG, "Use this token to send test notifications from Firebase Console")
        }
    }
}
