package com.high.ayodj.fileloader

import android.content.Context
import android.net.Uri
import androidx.test.core.app.ApplicationProvider
import org.junit.Assert.*
import org.junit.Test

class FileLoaderTest {
    private val context: Context = ApplicationProvider.getApplicationContext()

    @Test
    fun testAssetLoadSuccess() {
        val request = FileLoadRequest.Asset("sounds/sample1")
        val result = FileResolver.resolve(context, request)
        assertTrue(result.success)
        assertNotNull(result.data)
        assertEquals(SourceType.ASSET, result.sourceType)
    }

    @Test
    fun testLocalFileLoadFailure() {
        val request = FileLoadRequest.LocalFile("/invalid/path/file.mp3")
        val result = FileResolver.resolve(context, request)
        assertFalse(result.success)
        assertNotNull(result.error)
        assertEquals(SourceType.LOCAL_FILE, result.sourceType)
    }

    @Test
    fun testContentUriLoadFailure() {
        val uri = Uri.parse("content://invalid/uri")
        val request = FileLoadRequest.ContentUri(uri)
        val result = FileResolver.resolve(context, request)
        assertFalse(result.success)
        assertNotNull(result.error)
        assertEquals(SourceType.CONTENT_URI, result.sourceType)
    }

    @Test
    fun testCacheManagerPutAndGet() {
        val request = FileLoadRequest.Asset("sounds/sample1")
        val result = FileResolver.resolve(context, request)
        val entry = CacheEntry(request.toString(), result, System.currentTimeMillis())
        CacheManager.put(request, entry)
        val cached = CacheManager.get(request)
        assertNotNull(cached)
        assertEquals(entry, cached)
    }

    @Test
    fun testErrorHandlerUserMessage() {
        val msg = ErrorHandler.userMessage("No such file or directory")
        assertEquals("File not found.", msg)
    }
}
