package com.high.ayodj.fileloader

import com.high.ayodj.fileloader.CacheEntry
import com.high.ayodj.fileloader.FileLoadRequest

object CacheManager {
    private val cache = mutableMapOf<String, CacheEntry>()
    private const val CACHE_EXPIRY_MS = 10 * 60 * 1000 // 10 minutes

    fun get(request: FileLoadRequest): CacheEntry? {
        val key = request.toString()
        val entry = cache[key]
        if (entry != null && System.currentTimeMillis() - entry.timestamp < CACHE_EXPIRY_MS) {
            return entry
        }
        cache.remove(key)
        return null
    }

    fun put(request: FileLoadRequest, result: CacheEntry) {
        val key = request.toString()
        cache[key] = result
    }

    fun clear() {
        cache.clear()
    }

    fun invalidate(request: FileLoadRequest) {
        val key = request.toString()
        cache.remove(key)
    }
}
