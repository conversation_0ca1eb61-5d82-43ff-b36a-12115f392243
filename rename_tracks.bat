@echo off
cd /d "c:\Users\<USER>\AndroidStudioProjects\scratch gpro\app\src\main\assets\weekly"

echo Renaming files to match weekly drop convention...

ren "Aruarian Dance(rmx).mp3" "weekly_drop_w1_2025_a.mp3"
ren "back again.mp3" "weekly_drop_w1_2025_b.mp3"
ren "Champloos Journey.mp3" "weekly_drop_w2_2025_a.mp3"
ren "chop.mp3" "weekly_drop_w2_2025_b.mp3"
ren "Comes Back.mp3" "weekly_drop_w3_2025_a.mp3"
ren "DATES_TOO_REMEMBER_MASTER_1.mp3" "weekly_drop_w3_2025_b.mp3"
ren "shadowalley1.mp3" "weekly_drop_w4_2025_a.mp3"
ren "That's the shyt.mp3" "weekly_drop_w4_2025_b.mp3"
ren "The Overseer's Groove v2.mp3" "weekly_drop_w5_2025_a.mp3"
ren "You Never Knew.mp3" "weekly_drop_w5_2025_b.mp3"

echo File renaming complete!
pause