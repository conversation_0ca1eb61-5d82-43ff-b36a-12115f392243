package com.high.ayodj.fileloader

import com.high.ayodj.fileloader.SourceType

// Data class for JNI calls
data class JniCallData(
    val filePath: String?,
    val buffer: ByteArray?,
    val sourceType: SourceType
)

object JNIManager {
    init {
        System.loadLibrary("native-lib") // Update with your actual native library name
    }

    // Example native method signatures
    external fun loadSampleFromFile(path: String): Boolean
    external fun loadSampleFromBuffer(buffer: ByteArray): Boolean

    // Unified JNI call
    fun loadSample(data: JniCallData): Boolean {
        return when (data.sourceType) {
            SourceType.ASSET, SourceType.CONTENT_URI -> {
                // Use buffer for assets and content URIs
                data.buffer?.let { loadSampleFromBuffer(it) } ?: false
            }
            SourceType.LOCAL_FILE -> {
                // Use file path for local files
                data.filePath?.let { loadSampleFromFile(it) } ?: false
            }
        }
    }
}
