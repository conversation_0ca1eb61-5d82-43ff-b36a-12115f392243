1. we have an intro sound, but it is playing twice. we need to ensure it only plays a single time. 
2/4/5. max scratch speed is too low, we need to have that unified on the visual and audio, add max speed setting to menu for fine tune.
6. we arent getting the ayoDJ! logo adding to our app, just default android.
7. our notofication system and track loading system needs to be polished for release. 
8. we are getting artifacts after extended(minutes) use. check buffer overloads and memory leaks coming from the native audio side. only happens when music and scratch are being used simultaneously 

finish making audio tracks + add to google drive.(not ai'able)