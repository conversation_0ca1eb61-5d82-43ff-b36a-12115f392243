package com.high.ayodj.monetization

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * Manages trial period logic for new users
 */
class TrialManager(private val context: Context) {
    
    private val prefs: SharedPreferences = context.getSharedPreferences("trial_system", Context.MODE_PRIVATE)
    
    companion object {
        private const val TAG = "TrialManager"
        private const val TRIAL_START_KEY = "trial_start_timestamp"
        private const val HAS_TRIAL_STARTED_KEY = "has_trial_started"
        private const val TRIAL_DURATION_DAYS = 14L
        private const val TRIAL_INFO_SHOWN_KEY = "trial_info_shown"
    }
    
    /**
     * Initialize trial for first-time users
     */
    fun initializeTrial() {
        if (!hasTrialStarted()) {
            val currentTime = System.currentTimeMillis()
            prefs.edit()
                .putLong(TRIAL_START_KEY, currentTime)
                .putBoolean(HAS_TRIAL_STARTED_KEY, true)
                .apply()
            
            Log.i(TAG, "Trial started for new user at ${Date(currentTime)}")
        }
    }
    
    /**
     * Check if trial has been started (user has used app before)
     */
    fun hasTrialStarted(): Boolean {
        return prefs.getBoolean(HAS_TRIAL_STARTED_KEY, false)
    }
    
    /**
     * Check if trial is currently active
     */
    fun isTrialActive(): Boolean {
        val trialStart = prefs.getLong(TRIAL_START_KEY, 0L)
        if (trialStart == 0L) return false
        
        val trialEnd = trialStart + TimeUnit.DAYS.toMillis(TRIAL_DURATION_DAYS)
        return System.currentTimeMillis() < trialEnd
    }
    
    /**
     * Get number of trial days remaining
     */
    fun getTrialDaysRemaining(): Int {
        val trialStart = prefs.getLong(TRIAL_START_KEY, 0L)
        if (trialStart == 0L) return 0
        
        val trialEnd = trialStart + TimeUnit.DAYS.toMillis(TRIAL_DURATION_DAYS)
        val timeRemaining = trialEnd - System.currentTimeMillis()
        return maxOf(0, TimeUnit.MILLISECONDS.toDays(timeRemaining).toInt())
    }
    
    /**
     * Get trial end date
     */
    fun getTrialEndDate(): Date {
        val trialStart = prefs.getLong(TRIAL_START_KEY, 0L)
        val trialEnd = trialStart + TimeUnit.DAYS.toMillis(TRIAL_DURATION_DAYS)
        return Date(trialEnd)
    }
    
    /**
     * Check if trial info popup should be shown
     */
    fun shouldShowTrialInfo(): Boolean {
        return isTrialActive() && !hasTrialInfoBeenShown()
    }
    
    /**
     * Mark trial info as shown
     */
    fun markTrialInfoShown() {
        prefs.edit().putBoolean(TRIAL_INFO_SHOWN_KEY, true).apply()
    }
    
    /**
     * Check if trial info has been shown
     */
    private fun hasTrialInfoBeenShown(): Boolean {
        return prefs.getBoolean(TRIAL_INFO_SHOWN_KEY, false)
    }
    
    /**
     * Get current user status based on trial and subscription
     */
    fun getCurrentStatus(hasPremiumSubscription: Boolean): SubscriptionStatus {
        return when {
            hasPremiumSubscription -> SubscriptionStatus.FULL_PREMIUM
            isTrialActive() -> SubscriptionStatus.TRIAL
            else -> SubscriptionStatus.FREE
        }
    }
    
    /**
     * Get formatted time remaining for UI display
     */
    fun getTrialTimeRemainingFormatted(): String {
        val daysRemaining = getTrialDaysRemaining()
        return when {
            daysRemaining > 1 -> "$daysRemaining days remaining"
            daysRemaining == 1 -> "1 day remaining"
            else -> "Trial expired"
        }
    }
    
    /**
     * Reset trial (for testing purposes only)
     */
    fun resetTrial() {
        prefs.edit()
            .remove(TRIAL_START_KEY)
            .remove(HAS_TRIAL_STARTED_KEY)
            .remove(TRIAL_INFO_SHOWN_KEY)
            .apply()
        Log.w(TAG, "Trial reset for testing")
    }
}
