# AYODJ Audio System Architecture - Complete Technical Documentation

## Overview
This document maps the complete AYODJ audio architecture, identifying all data flows, control systems, and critical interaction points that affect audio position accuracy.

---

## 1. SYSTEM OVERVIEW

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           AYODJ AUDIO SYSTEM ARCHITECTURE                      │
└─────────────────────────────────────────────────────────────────────────────────┘

┌─────────────────┐    ┌──────────────────┐    ┌─────────────────────────────────┐
│   UI LAYER      │    │   TOUCH SYSTEM   │    │       AUDIO ENGINE              │
│   (<PERSON><PERSON><PERSON>)      │    │   (<PERSON><PERSON><PERSON>)       │    │       (C++)                     │
├─────────────────┤    ├──────────────────┤    ├─────────────────────────────────┤
│ • PlatterView   │◄───┤ • Touch Events   │───►│ • AudioEngine Class             │
│ • Vinyl Angle   │    │ • Angle Calc     │    │ • Audio Callback (Oboe)         │
│ • Visual Sync   │    │ • Rate Calc      │    │ • Sample Management             │
│ • Sliders       │    │ • Sync Offset    │    │ • Master Tick System            │
└─────────────────┘    └──────────────────┘    └─────────────────────────────────┘
        ▲                        │                            │
        │                        ▼                            ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────────────────────┐
│  VINYL TRACKER  │    │   SLIP PHYSICS   │    │      AUDIO SAMPLE               │
│  (C++ Thread)   │    │   (Kotlin)       │    │      (C++)                      │
├─────────────────┤    ├──────────────────┤    ├─────────────────────────────────┤
│ • 360Hz Thread  │    │ • Motor Physics  │    │ • Position Tracking             │
│ • Angle Track   │    │ • Deceleration   │    │ • Sinc Interpolation            │
│ • Master Sync   │    │ • Rate Output    │    │ • Loop Management               │
└─────────────────┘    └──────────────────┘    └─────────────────────────────────┘
```

---

## 2. CRITICAL DATA FLOWS

### 2.1 Touch Input Flow (DIRECT CONTROL)
```
User Touch → PlatterTouch.kt → TouchSync calculation → Rate/Angle Delta
     ↓
scratchPlatterActiveInternal(isActiveTouch=true, angleDelta) 
     ↓
useEngineRateForPlayback_.store(true) + platterTargetPlaybackRate_.store(rate)
     ↓
AudioSample::getAudio() → Position Advancement → preciseCurrentFrame.store()
```

### 2.2 Motor Control Flow (PHYSICS-BASED)
```
SlipmatPhysics.kt → Motor Rate → AudioEngine callback
     ↓
MasterTickSystem.incrementTick() → Tick-based positioning
     ↓
AudioSample::getAudio() → Position Advancement → preciseCurrentFrame.store()
```

### 2.3 Visual Sync Flow (UI UPDATES)
```
VinylTracker (360Hz) → getCurrentVinylAngle() → PlatterView
     ↓
Master Tick System → ticksToVinylAngle() → Visual Rotation
```

---

## 3. POSITION CORRUPTION ROOT CAUSES

### 3.1 Cache System Interference (FIXED!)
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                         CACHE INTERFERENCE FLOW                                │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  PROBLEM (FIXED):                                                              │
│  ┌─────────────────────────────────────────────────────────────────────────┐  │
│  │ 1. Touch starts → useEngineRateForPlayback = false                     │  │
│  │ 2. Cache hit during touch → RESET ALL STATE (position=0, mode=true)   │  │
│  │ 3. Position jumps to 0 → 66.49 frame corruption occurs               │  │
│  └─────────────────────────────────────────────────────────────────────────┘  │
│                                                                                 │
│  SOLUTION (IMPLEMENTED):                                                       │
│  ┌─────────────────────────────────────────────────────────────────────────┐  │
│  │ 1. Check if currently in active use (touch || playing)                 │  │
│  │ 2. IF active: PRESERVE current state, don't reset anything             │  │
│  │ 3. IF inactive: Safe to reset state normally                           │  │
│  │ 4. Cache operations no longer cause position corruption                 │  │
│  └─────────────────────────────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 3.2 Fixed Code Locations
```cpp
// BEFORE (Lines 471-476):
if (activePlatterSample_) {
    activePlatterSample_->preciseCurrentFrame.store(0.0f);  // ← CORRUPTION!
    activePlatterSample_->useEngineRateForPlayback_.store(true); // ← MODE RESET!
}

// AFTER (Cache-Aware State Preservation):
bool isActivelyInUse = isFingerDownOnPlatter_.load() || 
                      (activePlatterSample_ && activePlatterSample_->isPlaying.load());

if (!isActivelyInUse) {
    // Safe to reset - no active interaction
    activePlatterSample_->preciseCurrentFrame.store(0.0f);
    // ... other resets
} else {
    // PRESERVE current state during active use
    ALOGI("CACHE_HIT: PRESERVED state - preventing corruption!");
}
```

---

## 4. CONTROL STATE MACHINE

### 4.1 Touch Control Mode
```
STATE: TOUCH_ACTIVE
┌─────────────────────────────────────┐
│ • isFingerDownOnPlatter = true      │
│ • useEngineRateForPlayback = true   │  
│ • Direct rate from touch input      │
│ • AudioSample advances position     │
│ • Master tick system passive       │
└─────────────────────────────────────┘
```

### 4.2 Motor Control Mode  
```
STATE: MOTOR_CONTROL
┌─────────────────────────────────────┐
│ • isFingerDownOnPlatter = false     │
│ • useEngineRateForPlayback = true   │
│ • Physics-calculated rate           │
│ • Master tick system active        │
│ • Tick-based position setting      │
└─────────────────────────────────────┘
```

### 4.3 Transition Handling
```
TOUCH → MOTOR:
┌─────────────────────────────────────┐
│ 1. releasePlatterTouchInternal()    │
│ 2. Sync master tick to audio pos   │
│ 3. Switch to motor control          │
│ 4. Seamless position continuity    │
└─────────────────────────────────────┘

MOTOR → TOUCH:
┌─────────────────────────────────────┐
│ 1. scratchPlatterActiveInternal()   │
│ 2. Store current position           │
│ 3. Switch to touch control          │
│ 4. Direct rate application          │
└─────────────────────────────────────┘
```

---

## 5. DEBUGGING SYSTEMS

### 5.1 Position Corruption Detection
```cpp
// Real-time corruption monitoring:
float positionBefore = activePlatterSample_->preciseCurrentFrame.load();
// ... rate change operations
float positionAfter = activePlatterSample_->preciseCurrentFrame.load();

if (std::abs(positionAfter - positionBefore) > 10.0f) {
    ALOGE("POSITION_CORRUPTION_DETECTED: %.2f -> %.2f (delta=%.2f)",
          positionBefore, positionAfter, positionAfter - positionBefore);
}
```

### 5.2 Control Mode Transition Tracking
```cpp
// Mode change detection:
ALOGI("CONTROL_MODE_TRANSITION: %s -> %s", 
      wasLastTouch ? "TOUCH" : "MOTOR", 
      isActiveTouch ? "TOUCH" : "MOTOR");
```

### 5.3 Cache Operation Monitoring
```cpp
// Cache hit state preservation:
ALOGI("CACHE_HIT_USER: Sample '%s' - Touch=%d, Playing=%d, ActiveUse=%d", 
      uriString.c_str(), isCurrentlyTouched, isCurrentlyPlaying, isActivelyInUse);

if (!isActivelyInUse) {
    ALOGI("CACHE_HIT_USER: SAFE state reset");
} else {
    ALOGI("CACHE_HIT_USER: PRESERVED state - preventing corruption!");
}
```

---

## 6. PERFORMANCE CHARACTERISTICS

### 6.1 Audio Processing
- **Sample Rate**: 48kHz (48,000 samples/second)
- **Buffer Size**: Variable (typically 128-512 frames)
- **Latency**: < 20ms total system latency
- **Interpolation**: 10-tap Kaiser-windowed sinc

### 6.2 Visual Sync
- **VinylTracker**: 360Hz update rate
- **PlatterView**: 60Hz UI refresh
- **Tick System**: 48kHz precision, 360Hz visual sync

### 6.3 Cache System
- **In-Memory**: LRU cache, 2 samples per type
- **Persistent**: File-based cache with staleness detection
- **State Preservation**: Active use detection prevents corruption

---

## 7. TESTING METHODOLOGY

### 7.1 Position Corruption Tests
1. **Start audio playback**
2. **Begin touch control** (monitor for mode transition)
3. **Switch between samples** (trigger cache operations)
4. **Monitor logs** for "POSITION_CORRUPTION_DETECTED"
5. **Verify smooth position continuity**

### 7.2 Cache State Preservation Tests
1. **Touch platter during playback**
2. **Load different sample** (trigger cache hit)
3. **Verify logs show** "PRESERVED state"
4. **Confirm no position jumps**

### 7.3 Control Mode Transition Tests
1. **Switch between touch and motor control**
2. **Monitor** "CONTROL_MODE_TRANSITION" logs
3. **Verify seamless rate changes**
4. **Check position synchronization**

---

## 8. ARCHITECTURE ACHIEVEMENTS

✅ **Cache-Aware State Preservation** - Prevents position resets during active use  
✅ **Comprehensive Debug Logging** - Real-time corruption detection  
✅ **Control Mode Tracking** - Clear touch/motor transition monitoring  
✅ **Position Integrity** - Eliminated 66.49 frame jump corruption  
✅ **Seamless Cache Operations** - No interruption during active playback  

---

## 9. MAINTENANCE NOTES

### 9.1 Critical Code Sections
- **Lines 471-498**: User sample cache hit handling
- **Lines 579-608**: Asset sample cache hit handling  
- **Lines 1496-1570**: Control mode switching logic
- **AudioSample.cpp**: Position advancement implementation

### 9.2 Monitoring Points
- Watch for "POSITION_CORRUPTION_DETECTED" in logs
- Monitor "CACHE_HIT: PRESERVED state" messages
- Track "CONTROL_MODE_TRANSITION" for smooth switching
- Verify no unexpected position resets

### 9.3 Future Enhancements
- Implement full Position Manager pattern for single authority
- Add state machine for formal control mode management
- Enhance transition smoothing algorithms
- Optimize cache hit performance

---

**Last Updated**: July 30, 2025  
**Status**: Position corruption issues resolved ✅  
**Next Phase**: Performance optimization and enhanced state management
