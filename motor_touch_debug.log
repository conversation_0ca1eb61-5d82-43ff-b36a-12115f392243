
08-02 00:35:27.726 29948 29948 D TouchSync: === MOTOR-TO-TOUCH TRANSITION START ===
08-02 00:35:27.726 29948 29948 D TouchSync: COORDINATE_SYNC: fingerAngle=46.518612┬░, visualAngle=285.0┬░, audioAngle=285.0┬░
08-02 00:35:27.726 29948 29948 D TouchSync: OFFSET_ANALYSIS: visual_offset=121.518616┬░, audio_offset=121.518616┬░
08-02 00:35:27.726 29948 29948 D TouchSync: HANDOFF_SYNC: Motor position maintained, touch starts from audio reference frame
08-02 00:35:27.726 29948 29948 I AudioEngine: MOTOR_TO_MANUAL_SYNC: Transition detected - <PERSON><PERSON><PERSON><PERSON><PERSON> SYSTEM HANDOFF
08-02 00:35:27.726 29948 29948 I AudioEngine: COORDINATE_HANDOFF: audio_pos=3648.00, physics_rate=1.0000, stored_rate=1.0000
08-02 00:35:27.726 29948 29948 I AudioEngine: MOTOR_TO_MANUAL_SYNC: No sync performed - testing coordinate handoff
08-02 00:35:27.726 29948 29948 D TouchSync: === MOTOR-TO-TOUCH TRANSITION COMPLETE ===
08-02 00:35:27.726 29948 29948 D TouchSync: RAW rate mapping: sending delta 0.0┬░ (was 0.0┬░) - visual stays synced with audio
08-02 00:35:27.735 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.1071311┬░ (was -1.1071311┬░) - visual stays synced with audio
08-02 00:35:27.742 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.2432734┬░ (was -1.2432734┬░) - visual stays synced with audio
08-02 00:35:27.750 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.3006117┬░ (was -1.3006117┬░) - visual stays synced with audio
08-02 00:35:27.759 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.42008┬░ (was -1.42008┬░) - visual stays synced with audio
08-02 00:35:27.767 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.4961264┬░ (was -1.4961264┬░) - visual stays synced with audio
08-02 00:35:27.776 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.4740497┬░ (was -1.4740497┬░) - visual stays synced with audio
08-02 00:35:27.784 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.496858┬░ (was -1.496858┬░) - visual stays synced with audio
08-02 00:35:27.792 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.3918087┬░ (was -1.3918087┬░) - visual stays synced with audio
08-02 00:35:27.801 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.1213411┬░ (was -1.1213411┬░) - visual stays synced with audio
08-02 00:35:27.809 29948 29948 D TouchSync: RAW rate mapping: sending delta -0.94057906┬░ (was -0.94057906┬░) - visual stays synced with audio
08-02 00:35:27.821 29948 29948 I AppViewModel_Touch: onPlatterTouchUp() called - releasing platter touch
08-02 00:35:27.822 29948 29948 I AppViewModel_Touch: Calling onReleasePlatterTouch()
08-02 00:35:27.822 29948 29948 I AudioEngine: TOUCH_TO_MOTOR_HANDOFF: audio_pos=2305.08, physics_rate=-0.9406, stored_rate=-0.8777
08-02 00:35:27.822 29948 29948 I AudioEngine: COORDINATE_TRANSITION: TouchΓåÆMotor, Physics rate -0.9406 ΓåÆ Target 1.0000
08-02 00:35:27.822 29948 29948 I AppViewModel_Touch: onReleasePlatterTouch() completed
08-02 00:35:28.509 29948 29948 D TouchSync: === MOTOR-TO-TOUCH TRANSITION START ===
08-02 00:35:28.509 29948 29948 D TouchSync: COORDINATE_SYNC: fingerAngle=47.102226┬░, visualAngle=310.34192┬░, audioAngle=310.34192┬░
08-02 00:35:28.509 29948 29948 D TouchSync: OFFSET_ANALYSIS: visual_offset=96.760315┬░, audio_offset=96.760315┬░
08-02 00:35:28.509 29948 29948 D TouchSync: HANDOFF_SYNC: Motor position maintained, touch starts from audio reference frame
08-02 00:35:28.509 29948 29948 I AudioEngine: MOTOR_TO_MANUAL_SYNC: Transition detected - COORDINATE SYSTEM HANDOFF
08-02 00:35:28.509 29948 29948 I AudioEngine: COORDINATE_HANDOFF: audio_pos=86.85, physics_rate=0.9176, stored_rate=0.9129
08-02 00:35:28.509 29948 29948 I AudioEngine: MOTOR_TO_MANUAL_SYNC: No sync performed - testing coordinate handoff
08-02 00:35:28.509 29948 29948 D TouchSync: === MOTOR-TO-TOUCH TRANSITION COMPLETE ===
08-02 00:35:28.509 29948 29948 D TouchSync: RAW rate mapping: sending delta 0.0┬░ (was 0.0┬░) - visual stays synced with audio
08-02 00:35:28.518 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.3516945┬░ (was -1.3516945┬░) - visual stays synced with audio
08-02 00:35:28.526 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.2641782┬░ (was -1.2641782┬░) - visual stays synced with audio
08-02 00:35:28.535 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.2639741┬░ (was -1.2639741┬░) - visual stays synced with audio
08-02 00:35:28.543 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.4091644┬░ (was -1.4091644┬░) - visual stays synced with audio
08-02 00:35:28.552 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.4389682┬░ (was -1.4389682┬░) - visual stays synced with audio
08-02 00:35:28.559 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.5259184┬░ (was -1.5259184┬░) - visual stays synced with audio
08-02 00:35:28.568 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.3884724┬░ (was -1.3884724┬░) - visual stays synced with audio
08-02 00:35:28.581 29948 29948 I AppViewModel_Touch: onPlatterTouchUp() called - releasing platter touch
08-02 00:35:28.581 29948 29948 I AppViewModel_Touch: Calling onReleasePlatterTouch()
08-02 00:35:28.581 29948 29948 I AudioEngine: TOUCH_TO_MOTOR_HANDOFF: audio_pos=13959.83, physics_rate=-1.3885, stored_rate=-0.7334
08-02 00:35:28.581 29948 29948 I AudioEngine: COORDINATE_TRANSITION: TouchΓåÆMotor, Physics rate -1.3885 ΓåÆ Target 1.0000
08-02 00:35:28.581 29948 29948 I AppViewModel_Touch: onReleasePlatterTouch() completed
08-02 00:35:28.909 29948 29948 D TouchSync: === MOTOR-TO-TOUCH TRANSITION START ===
08-02 00:35:28.909 29948 29948 D TouchSync: COORDINATE_SYNC: fingerAngle=40.756844┬░, visualAngle=283.12622┬░, audioAngle=283.12622┬░
08-02 00:35:28.909 29948 29948 D TouchSync: OFFSET_ANALYSIS: visual_offset=117.630615┬░, audio_offset=117.630615┬░
08-02 00:35:28.909 29948 29948 D TouchSync: HANDOFF_SYNC: Motor position maintained, touch starts from audio reference frame
08-02 00:35:28.909 29948 29948 I AudioEngine: MOTOR_TO_MANUAL_SYNC: Transition detected - COORDINATE SYSTEM HANDOFF
08-02 00:35:28.909 29948 29948 I AudioEngine: COORDINATE_HANDOFF: audio_pos=9233.33, physics_rate=0.4501, stored_rate=0.4196
08-02 00:35:28.909 29948 29948 I AudioEngine: MOTOR_TO_MANUAL_SYNC: No sync performed - testing coordinate handoff
08-02 00:35:28.909 29948 29948 D TouchSync: === MOTOR-TO-TOUCH TRANSITION COMPLETE ===
08-02 00:35:28.909 29948 29948 D TouchSync: RAW rate mapping: sending delta 0.0┬░ (was 0.0┬░) - visual stays synced with audio
08-02 00:35:28.918 29948 29948 D TouchSync: RAW rate mapping: sending delta 1.0551804┬░ (was 1.0551804┬░) - visual stays synced with audio
08-02 00:35:28.926 29948 29948 D TouchSync: RAW rate mapping: sending delta 1.2112045┬░ (was 1.2112045┬░) - visual stays synced with audio
08-02 00:35:28.934 29948 29948 D TouchSync: RAW rate mapping: sending delta 1.3846813┬░ (was 1.3846813┬░) - visual stays synced with audio
08-02 00:35:28.943 29948 29948 D TouchSync: RAW rate mapping: sending delta 1.4209031┬░ (was 1.4209031┬░) - visual stays synced with audio
08-02 00:35:28.951 29948 29948 D TouchSync: RAW rate mapping: sending delta 1.5753301┬░ (was 1.5753301┬░) - visual stays synced with audio
08-02 00:35:28.960 29948 29948 D TouchSync: RAW rate mapping: sending delta 1.6300213┬░ (was 1.6300213┬░) - visual stays synced with audio
08-02 00:35:28.968 29948 29948 D TouchSync: RAW rate mapping: sending delta 1.703656┬░ (was 1.703656┬░) - visual stays synced with audio
08-02 00:35:28.977 29948 29948 D TouchSync: RAW rate mapping: sending delta 1.7218786┬░ (was 1.7218786┬░) - visual stays synced with audio
08-02 00:35:28.985 29948 29948 D TouchSync: RAW rate mapping: sending delta 1.6726158┬░ (was 1.6726158┬░) - visual stays synced with audio
08-02 00:35:28.998 29948 29948 I AppViewModel_Touch: onPlatterTouchUp() called - releasing platter touch
08-02 00:35:28.999 29948 29948 I AppViewModel_Touch: Calling onReleasePlatterTouch()
08-02 00:35:28.999 29948 29948 I AudioEngine: TOUCH_TO_MOTOR_HANDOFF: audio_pos=12725.35, physics_rate=1.6726, stored_rate=1.1789
08-02 00:35:28.999 29948 29948 I AudioEngine: COORDINATE_TRANSITION: TouchΓåÆMotor, Physics rate 1.6726 ΓåÆ Target 1.0000
08-02 00:35:28.999 29948 29948 I AppViewModel_Touch: onReleasePlatterTouch() completed
08-02 00:35:30.008 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=118.46001┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:35:30.026 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=120.96001┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:35:30.042 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=123.46001┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:35:31.211 29948 29948 D TouchSync: === MOTOR-TO-TOUCH TRANSITION START ===
08-02 00:35:31.211 29948 29948 D TouchSync: COORDINATE_SYNC: fingerAngle=54.711426┬░, visualAngle=298.46002┬░, audioAngle=298.46002┬░
08-02 00:35:31.211 29948 29948 D TouchSync: OFFSET_ANALYSIS: visual_offset=116.2514┬░, audio_offset=116.2514┬░
08-02 00:35:31.211 29948 29948 D TouchSync: HANDOFF_SYNC: Motor position maintained, touch starts from audio reference frame
08-02 00:35:31.211 29948 29948 I AudioEngine: MOTOR_TO_MANUAL_SYNC: Transition detected - COORDINATE SYSTEM HANDOFF
08-02 00:35:31.211 29948 29948 I AudioEngine: COORDINATE_HANDOFF: audio_pos=10004.57, physics_rate=1.0000, stored_rate=1.0000
08-02 00:35:31.211 29948 29948 I AudioEngine: MOTOR_TO_MANUAL_SYNC: No sync performed - testing coordinate handoff
08-02 00:35:31.211 29948 29948 D TouchSync: === MOTOR-TO-TOUCH TRANSITION COMPLETE ===
08-02 00:35:31.211 29948 29948 D TouchSync: RAW rate mapping: sending delta 0.0┬░ (was 0.0┬░) - visual stays synced with audio
08-02 00:35:31.220 29948 29948 D TouchSync: RAW rate mapping: sending delta -0.9500269┬░ (was -0.9500269┬░) - visual stays synced with audio
08-02 00:35:31.227 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.0937085┬░ (was -1.0937085┬░) - visual stays synced with audio
08-02 00:35:31.236 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.2528304┬░ (was -1.2528304┬░) - visual stays synced with audio
08-02 00:35:31.244 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.3729875┬░ (was -1.3729875┬░) - visual stays synced with audio
08-02 00:35:31.253 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.6801308┬░ (was -1.6801308┬░) - visual stays synced with audio
08-02 00:35:31.260 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.9283805┬░ (was -1.9283805┬░) - visual stays synced with audio
08-02 00:35:31.270 29948 29948 D TouchSync: RAW rate mapping: sending delta -2.0599668┬░ (was -2.0599668┬░) - visual stays synced with audio
08-02 00:35:31.278 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.4147254┬░ (was -1.4147254┬░) - visual stays synced with audio
08-02 00:35:31.278 29948 29948 I AppViewModel_Touch: onPlatterTouchUp() called - releasing platter touch
08-02 00:35:31.278 29948 29948 I AppViewModel_Touch: Calling onReleasePlatterTouch()
08-02 00:35:31.278 29948 29948 I AudioEngine: TOUCH_TO_MOTOR_HANDOFF: audio_pos=9888.89, physics_rate=-1.4147, stored_rate=-0.8058
08-02 00:35:31.278 29948 29948 I AudioEngine: COORDINATE_TRANSITION: TouchΓåÆMotor, Physics rate -1.4147 ΓåÆ Target 1.0000
08-02 00:35:31.278 29948 29948 I AppViewModel_Touch: onReleasePlatterTouch() completed
08-02 00:35:31.661 29948 29948 D TouchSync: === MOTOR-TO-TOUCH TRANSITION START ===
08-02 00:35:31.661 29948 29948 D TouchSync: COORDINATE_SYNC: fingerAngle=47.298164┬░, visualAngle=275.23315┬░, audioAngle=275.23315┬░
08-02 00:35:31.661 29948 29948 D TouchSync: OFFSET_ANALYSIS: visual_offset=132.065┬░, audio_offset=132.065┬░
08-02 00:35:31.661 29948 29948 D TouchSync: HANDOFF_SYNC: Motor position maintained, touch starts from audio reference frame
08-02 00:35:31.661 29948 29948 I AudioEngine: MOTOR_TO_MANUAL_SYNC: Transition detected - COORDINATE SYSTEM HANDOFF
08-02 00:35:31.661 29948 29948 I AudioEngine: COORDINATE_HANDOFF: audio_pos=6079.07, physics_rate=0.5665, stored_rate=0.5423
08-02 00:35:31.661 29948 29948 I AudioEngine: MOTOR_TO_MANUAL_SYNC: No sync performed - testing coordinate handoff
08-02 00:35:31.661 29948 29948 D TouchSync: === MOTOR-TO-TOUCH TRANSITION COMPLETE ===
08-02 00:35:31.661 29948 29948 D TouchSync: RAW rate mapping: sending delta 0.0┬░ (was 0.0┬░) - visual stays synced with audio
08-02 00:35:31.669 29948 29948 D TouchSync: RAW rate mapping: sending delta 1.1154766┬░ (was 1.1154766┬░) - visual stays synced with audio
08-02 00:35:31.677 29948 29948 D TouchSync: RAW rate mapping: sending delta 1.299227┬░ (was 1.299227┬░) - visual stays synced with audio
08-02 00:35:31.686 29948 29948 D TouchSync: RAW rate mapping: sending delta 1.4638271┬░ (was 1.4638271┬░) - visual stays synced with audio
08-02 00:35:31.695 29948 29948 D TouchSync: RAW rate mapping: sending delta 1.57184┬░ (was 1.57184┬░) - visual stays synced with audio
08-02 00:35:31.703 29948 29948 D TouchSync: RAW rate mapping: sending delta 1.5360017┬░ (was 1.5360017┬░) - visual stays synced with audio
08-02 00:35:31.711 29948 29948 D TouchSync: RAW rate mapping: sending delta 1.4692265┬░ (was 1.4692265┬░) - visual stays synced with audio
08-02 00:35:31.720 29948 29948 D TouchSync: RAW rate mapping: sending delta 1.3856463┬░ (was 1.3856463┬░) - visual stays synced with audio
08-02 00:35:31.728 29948 29948 D TouchSync: RAW rate mapping: sending delta 1.197017┬░ (was 1.197017┬░) - visual stays synced with audio
08-02 00:35:31.736 29948 29948 D TouchSync: RAW rate mapping: sending delta 0.98734826┬░ (was 0.98734826┬░) - visual stays synced with audio
08-02 00:35:31.744 29948 29948 D TouchSync: RAW rate mapping: sending delta 0.68714297┬░ (was 0.68714297┬░) - visual stays synced with audio
08-02 00:35:31.754 29948 29948 I AppViewModel_Touch: onPlatterTouchUp() called - releasing platter touch
08-02 00:35:31.754 29948 29948 I AppViewModel_Touch: Calling onReleasePlatterTouch()
08-02 00:35:31.754 29948 29948 I AudioEngine: TOUCH_TO_MOTOR_HANDOFF: audio_pos=9949.82, physics_rate=0.6871, stored_rate=1.0255
08-02 00:35:31.754 29948 29948 I AudioEngine: COORDINATE_TRANSITION: TouchΓåÆMotor, Physics rate 0.6871 ΓåÆ Target 1.0000
08-02 00:35:31.754 29948 29948 I AppViewModel_Touch: onReleasePlatterTouch() completed
08-02 00:35:31.995 29948 29948 D TouchSync: === MOTOR-TO-TOUCH TRANSITION START ===
08-02 00:35:31.995 29948 29948 D TouchSync: COORDINATE_SYNC: fingerAngle=53.60308┬░, visualAngle=321.78564┬░, audioAngle=321.78564┬░
08-02 00:35:31.995 29948 29948 D TouchSync: OFFSET_ANALYSIS: visual_offset=91.817444┬░, audio_offset=91.817444┬░
08-02 00:35:31.995 29948 29948 D TouchSync: HANDOFF_SYNC: Motor position maintained, touch starts from audio reference frame
08-02 00:35:31.995 29948 29948 I AudioEngine: MOTOR_TO_MANUAL_SYNC: Transition detected - COORDINATE SYSTEM HANDOFF
08-02 00:35:31.995 29948 29948 I AudioEngine: COORDINATE_HANDOFF: audio_pos=4912.25, physics_rate=0.8985, stored_rate=0.8927
08-02 00:35:31.995 29948 29948 I AudioEngine: MOTOR_TO_MANUAL_SYNC: No sync performed - testing coordinate handoff
08-02 00:35:31.995 29948 29948 D TouchSync: === MOTOR-TO-TOUCH TRANSITION COMPLETE ===
08-02 00:35:31.995 29948 29948 D TouchSync: RAW rate mapping: sending delta 0.0┬░ (was 0.0┬░) - visual stays synced with audio
08-02 00:35:32.003 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.0143703┬░ (was -1.0143703┬░) - visual stays synced with audio
08-02 00:35:32.011 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.2633314┬░ (was -1.2633314┬░) - visual stays synced with audio
08-02 00:35:32.020 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.7737987┬░ (was -1.7737987┬░) - visual stays synced with audio
08-02 00:35:32.028 29948 29948 D TouchSync: RAW rate mapping: sending delta -2.1442206┬░ (was -2.1442206┬░) - visual stays synced with audio
08-02 00:35:32.037 29948 29948 D TouchSync: RAW rate mapping: sending delta -2.417778┬░ (was -2.417778┬░) - visual stays synced with audio
08-02 00:35:32.045 29948 29948 D TouchSync: RAW rate mapping: sending delta -2.6609955┬░ (was -2.6609955┬░) - visual stays synced with audio
08-02 00:35:32.053 29948 29948 D TouchSync: RAW rate mapping: sending delta -2.7001395┬░ (was -2.7001395┬░) - visual stays synced with audio
08-02 00:35:32.061 29948 29948 D TouchSync: RAW rate mapping: sending delta -2.6915572┬░ (was -2.6915572┬░) - visual stays synced with audio
08-02 00:35:32.070 29948 29948 D TouchSync: RAW rate mapping: sending delta -2.245394┬░ (was -2.245394┬░) - visual stays synced with audio
08-02 00:35:32.078 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.6991115┬░ (was -1.6991115┬░) - visual stays synced with audio
08-02 00:35:32.087 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.38809┬░ (was -1.38809┬░) - visual stays synced with audio
08-02 00:35:32.095 29948 29948 D TouchSync: RAW rate mapping: sending delta -0.93209815┬░ (was -0.93209815┬░) - visual stays synced with audio
08-02 00:35:32.103 29948 29948 D TouchSync: RAW rate mapping: sending delta -0.3344434┬░ (was -0.3344434┬░) - visual stays synced with audio
08-02 00:35:32.105 29948 29948 I AppViewModel_Touch: onPlatterTouchUp() called - releasing platter touch
08-02 00:35:32.106 29948 29948 I AppViewModel_Touch: Calling onReleasePlatterTouch()
08-02 00:35:32.106 29948 29948 I AudioEngine: TOUCH_TO_MOTOR_HANDOFF: audio_pos=2368.90, physics_rate=-0.3344, stored_rate=-0.9650
08-02 00:35:32.106 29948 29948 I AudioEngine: COORDINATE_TRANSITION: TouchΓåÆMotor, Physics rate -0.3344 ΓåÆ Target 1.0000
08-02 00:35:32.106 29948 29948 I AppViewModel_Touch: onReleasePlatterTouch() completed
08-02 00:35:35.002 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=329.65778┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:35:35.019 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=332.15778┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:35:35.035 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=334.65778┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:35:36.357 29948 29948 D TouchSync: === MOTOR-TO-TOUCH TRANSITION START ===
08-02 00:35:36.357 29948 29948 D TouchSync: COORDINATE_SYNC: fingerAngle=44.63806┬░, visualAngle=174.65778┬░, audioAngle=174.65778┬░
08-02 00:35:36.357 29948 29948 D TouchSync: OFFSET_ANALYSIS: visual_offset=-130.01971┬░, audio_offset=-130.01971┬░
08-02 00:35:36.357 29948 29948 D TouchSync: HANDOFF_SYNC: Motor position maintained, touch starts from audio reference frame
08-02 00:35:36.357 29948 29948 I AudioEngine: MOTOR_TO_MANUAL_SYNC: Transition detected - COORDINATE SYSTEM HANDOFF
08-02 00:35:36.357 29948 29948 I AudioEngine: COORDINATE_HANDOFF: audio_pos=3650.29, physics_rate=1.0000, stored_rate=1.0000
08-02 00:35:36.357 29948 29948 I AudioEngine: MOTOR_TO_MANUAL_SYNC: No sync performed - testing coordinate handoff
08-02 00:35:36.357 29948 29948 D TouchSync: === MOTOR-TO-TOUCH TRANSITION COMPLETE ===
08-02 00:35:36.357 29948 29948 D TouchSync: RAW rate mapping: sending delta 0.0┬░ (was 0.0┬░) - visual stays synced with audio
08-02 00:35:36.364 29948 29948 D TouchSync: RAW rate mapping: sending delta -0.9753346┬░ (was -0.9753346┬░) - visual stays synced with audio
08-02 00:35:36.373 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.040199┬░ (was -1.040199┬░) - visual stays synced with audio
08-02 00:35:36.380 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.1296666┬░ (was -1.1296666┬░) - visual stays synced with audio
08-02 00:35:36.389 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.382123┬░ (was -1.382123┬░) - visual stays synced with audio
08-02 00:35:36.398 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.4970074┬░ (was -1.4970074┬░) - visual stays synced with audio
08-02 00:35:36.406 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.5012938┬░ (was -1.5012938┬░) - visual stays synced with audio
08-02 00:35:36.411 29948 29948 I AppViewModel_Touch: onPlatterTouchUp() called - releasing platter touch
08-02 00:35:36.411 29948 29948 I AppViewModel_Touch: Calling onReleasePlatterTouch()
08-02 00:35:36.411 29948 29948 I AudioEngine: TOUCH_TO_MOTOR_HANDOFF: audio_pos=3861.79, physics_rate=-1.5013, stored_rate=-0.5436
08-02 00:35:36.411 29948 29948 I AudioEngine: COORDINATE_TRANSITION: TouchΓåÆMotor, Physics rate -1.5013 ΓåÆ Target 1.0000
08-02 00:35:36.411 29948 29948 I AppViewModel_Touch: onReleasePlatterTouch() completed
08-02 00:35:36.881 29948 29948 D TouchSync: === MOTOR-TO-TOUCH TRANSITION START ===
08-02 00:35:36.881 29948 29948 D TouchSync: COORDINATE_SYNC: fingerAngle=35.910675┬░, visualAngle=163.94666┬░, audioAngle=163.94666┬░
08-02 00:35:36.881 29948 29948 D TouchSync: OFFSET_ANALYSIS: visual_offset=-128.03598┬░, audio_offset=-128.03598┬░
08-02 00:35:36.881 29948 29948 D TouchSync: HANDOFF_SYNC: Motor position maintained, touch starts from audio reference frame
08-02 00:35:36.881 29948 29948 I AudioEngine: MOTOR_TO_MANUAL_SYNC: Transition detected - COORDINATE SYSTEM HANDOFF
08-02 00:35:36.881 29948 29948 I AudioEngine: COORDINATE_HANDOFF: audio_pos=2044.14, physics_rate=0.7008, stored_rate=0.6841
08-02 00:35:36.881 29948 29948 I AudioEngine: MOTOR_TO_MANUAL_SYNC: No sync performed - testing coordinate handoff
08-02 00:35:36.881 29948 29948 D TouchSync: === MOTOR-TO-TOUCH TRANSITION COMPLETE ===
08-02 00:35:36.881 29948 29948 D TouchSync: RAW rate mapping: sending delta 0.0┬░ (was 0.0┬░) - visual stays synced with audio
08-02 00:35:36.889 29948 29948 D TouchSync: RAW rate mapping: sending delta 1.121758┬░ (was 1.121758┬░) - visual stays synced with audio
08-02 00:35:36.898 29948 29948 D TouchSync: RAW rate mapping: sending delta 1.3574071┬░ (was 1.3574071┬░) - visual stays synced with audio
08-02 00:35:36.906 29948 29948 D TouchSync: RAW rate mapping: sending delta 1.6269634┬░ (was 1.6269634┬░) - visual stays synced with audio
08-02 00:35:36.914 29948 29948 D TouchSync: RAW rate mapping: sending delta 1.9412595┬░ (was 1.9412595┬░) - visual stays synced with audio
08-02 00:35:36.924 29948 29948 D TouchSync: RAW rate mapping: sending delta 1.9959882┬░ (was 1.9959882┬░) - visual stays synced with audio
08-02 00:35:36.931 29948 29948 D TouchSync: RAW rate mapping: sending delta 2.0229754┬░ (was 2.0229754┬░) - visual stays synced with audio
08-02 00:35:36.940 29948 29948 D TouchSync: RAW rate mapping: sending delta 2.070791┬░ (was 2.070791┬░) - visual stays synced with audio
08-02 00:35:36.948 29948 29948 D TouchSync: RAW rate mapping: sending delta 1.892199┬░ (was 1.892199┬░) - visual stays synced with audio
08-02 00:35:36.957 29948 29948 D TouchSync: RAW rate mapping: sending delta 1.5932038┬░ (was 1.5932038┬░) - visual stays synced with audio
08-02 00:35:36.964 29948 29948 D TouchSync: RAW rate mapping: sending delta 1.3432552┬░ (was 1.3432552┬░) - visual stays synced with audio
08-02 00:35:36.973 29948 29948 D TouchSync: RAW rate mapping: sending delta 1.1461458┬░ (was 1.1461458┬░) - visual stays synced with audio
08-02 00:35:36.981 29948 29948 D TouchSync: RAW rate mapping: sending delta 0.99661905┬░ (was 0.99661905┬░) - visual stays synced with audio
08-02 00:35:36.990 29948 29948 D TouchSync: RAW rate mapping: sending delta 0.85821974┬░ (was 0.85821974┬░) - visual stays synced with audio
08-02 00:35:36.991 29948 29948 I AppViewModel_Touch: onPlatterTouchUp() called - releasing platter touch
08-02 00:35:36.991 29948 29948 I AppViewModel_Touch: Calling onReleasePlatterTouch()
08-02 00:35:36.991 29948 29948 I AudioEngine: TOUCH_TO_MOTOR_HANDOFF: audio_pos=7281.72, physics_rate=0.8582, stored_rate=1.1535
08-02 00:35:36.991 29948 29948 I AudioEngine: COORDINATE_TRANSITION: TouchΓåÆMotor, Physics rate 0.8582 ΓåÆ Target 1.0000
08-02 00:35:36.991 29948 29948 I AppViewModel_Touch: onReleasePlatterTouch() completed
08-02 00:35:37.432 29948 29948 D TouchSync: === MOTOR-TO-TOUCH TRANSITION START ===
08-02 00:35:37.432 29948 29948 D TouchSync: COORDINATE_SYNC: fingerAngle=53.51995┬░, visualAngle=251.18462┬░, audioAngle=251.18462┬░
08-02 00:35:37.432 29948 29948 D TouchSync: OFFSET_ANALYSIS: visual_offset=162.33533┬░, audio_offset=162.33533┬░
08-02 00:35:37.432 29948 29948 D TouchSync: HANDOFF_SYNC: Motor position maintained, touch starts from audio reference frame
08-02 00:35:37.432 29948 29948 I AudioEngine: MOTOR_TO_MANUAL_SYNC: Transition detected - COORDINATE SYSTEM HANDOFF
08-02 00:35:37.432 29948 29948 I AudioEngine: COORDINATE_HANDOFF: audio_pos=12861.86, physics_rate=0.9824, stored_rate=0.9814
08-02 00:35:37.432 29948 29948 I AudioEngine: MOTOR_TO_MANUAL_SYNC: No sync performed - testing coordinate handoff
08-02 00:35:37.432 29948 29948 D TouchSync: === MOTOR-TO-TOUCH TRANSITION COMPLETE ===
08-02 00:35:37.432 29948 29948 D TouchSync: RAW rate mapping: sending delta 0.0┬░ (was 0.0┬░) - visual stays synced with audio
08-02 00:35:37.440 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.4916897┬░ (was -1.4916897┬░) - visual stays synced with audio
08-02 00:35:37.449 29948 29948 D TouchSync: RAW rate mapping: sending delta -1.7071583┬░ (was -1.7071583┬░) - visual stays synced with audio
08-02 00:35:37.457 29948 29948 D TouchSync: RAW rate mapping: sending delta -2.2556272┬░ (was -2.2556272┬░) - visual stays synced with audio
08-02 00:35:37.465 29948 29948 D TouchSync: RAW rate mapping: sending delta -2.5107956┬░ (was -2.5107956┬░) - visual stays synced with audio
08-02 00:35:37.473 29948 29948 D TouchSync: RAW rate mapping: sending delta -2.6497412┬░ (was -2.6497412┬░) - visual stays synced with audio
08-02 00:35:37.482 29948 29948 D TouchSync: RAW rate mapping: sending delta -2.5471892┬░ (was -2.5471892┬░) - visual stays synced with audio
08-02 00:35:37.490 29948 29948 D TouchSync: RAW rate mapping: sending delta -2.2081776┬░ (was -2.2081776┬░) - visual stays synced with audio
08-02 00:35:37.497 29948 29948 D TouchSync: RAW rate mapping: sending delta -0.7872949┬░ (was -0.7872949┬░) - visual stays synced with audio
08-02 00:35:37.498 29948 29948 I AppViewModel_Touch: onPlatterTouchUp() called - releasing platter touch
08-02 00:35:37.498 29948 29948 I AppViewModel_Touch: Calling onReleasePlatterTouch()
08-02 00:35:37.498 29948 29948 I AudioEngine: TOUCH_TO_MOTOR_HANDOFF: audio_pos=12396.34, physics_rate=-0.7873, stored_rate=-0.7907
08-02 00:35:37.498 29948 29948 I AudioEngine: COORDINATE_TRANSITION: TouchΓåÆMotor, Physics rate -0.7873 ΓåÆ Target 1.0000
08-02 00:35:37.498 29948 29948 I AppViewModel_Touch: onReleasePlatterTouch() completed
08-02 00:35:38.933 29948 29948 D TouchSync: === MOTOR-TO-TOUCH TRANSITION START ===
08-02 00:35:38.933 29948 29948 D TouchSync: COORDINATE_SYNC: fingerAngle=36.80628┬░, visualAngle=31.57428┬░, audioAngle=31.57428┬░
08-02 00:35:38.933 29948 29948 D TouchSync: OFFSET_ANALYSIS: visual_offset=5.2319984┬░, audio_offset=5.2319984┬░
08-02 00:35:38.933 29948 29948 D TouchSync: HANDOFF_SYNC: Motor position maintained, touch starts from audio reference frame
08-02 00:35:38.933 29948 29948 I AudioEngine: MOTOR_TO_MANUAL_SYNC: Transition detected - COORDINATE SYSTEM HANDOFF
08-02 00:35:38.933 29948 29948 I AudioEngine: COORDINATE_HANDOFF: audio_pos=3697.80, physics_rate=1.0000, stored_rate=1.0000
08-02 00:35:38.933 29948 29948 I AudioEngine: MOTOR_TO_MANUAL_SYNC: No sync performed - testing coordinate handoff
08-02 00:35:38.933 29948 29948 D TouchSync: === MOTOR-TO-TOUCH TRANSITION COMPLETE ===
08-02 00:35:38.933 29948 29948 D TouchSync: RAW rate mapping: sending delta 0.0┬░ (was 0.0┬░) - visual stays synced with audio
08-02 00:35:38.942 29948 29948 D TouchSync: RAW rate mapping: sending delta -0.4554412┬░ (was -0.4554412┬░) - visual stays synced with audio
08-02 00:35:38.949 29948 29948 D TouchSync: RAW rate mapping: sending delta -0.21328738┬░ (was -0.21328738┬░) - visual stays synced with audio
08-02 00:35:38.961 29948 29948 I AppViewModel_Touch: onPlatterTouchUp() called - releasing platter touch
08-02 00:35:38.961 29948 29948 I AppViewModel_Touch: Calling onReleasePlatterTouch()
08-02 00:35:38.962 29948 29948 I AudioEngine: TOUCH_TO_MOTOR_HANDOFF: audio_pos=4450.59, physics_rate=-0.2133, stored_rate=0.4319
08-02 00:35:38.962 29948 29948 I AudioEngine: COORDINATE_TRANSITION: TouchΓåÆMotor, Physics rate -0.2133 ΓåÆ Target 1.0000
08-02 00:35:38.962 29948 29948 I AppViewModel_Touch: onReleasePlatterTouch() completed
08-02 00:35:39.583 29948 29948 D TouchSync: === MOTOR-TO-TOUCH TRANSITION START ===
08-02 00:35:39.583 29948 29948 D TouchSync: COORDINATE_SYNC: fingerAngle=39.77827┬░, visualAngle=86.31031┬░, audioAngle=86.31031┬░
08-02 00:35:39.583 29948 29948 D TouchSync: OFFSET_ANALYSIS: visual_offset=-46.53204┬░, audio_offset=-46.53204┬░
08-02 00:35:39.583 29948 29948 D TouchSync: HANDOFF_SYNC: Motor position maintained, touch starts from audio reference frame
08-02 00:35:39.583 29948 29948 I AudioEngine: MOTOR_TO_MANUAL_SYNC: Transition detected - COORDINATE SYSTEM HANDOFF
08-02 00:35:39.583 29948 29948 I AudioEngine: COORDINATE_HANDOFF: audio_pos=7616.64, physics_rate=0.9325, stored_rate=0.9286
08-02 00:35:39.583 29948 29948 I AudioEngine: MOTOR_TO_MANUAL_SYNC: No sync performed - testing coordinate handoff
08-02 00:35:39.583 29948 29948 D TouchSync: === MOTOR-TO-TOUCH TRANSITION COMPLETE ===
08-02 00:35:39.583 29948 29948 D TouchSync: RAW rate mapping: sending delta 0.0┬░ (was 0.0┬░) - visual stays synced with audio
08-02 00:35:39.590 29948 29948 D TouchSync: RAW rate mapping: sending delta 0.25585255┬░ (was 0.25585255┬░) - visual stays synced with audio
08-02 00:35:39.600 29948 29948 D TouchSync: RAW rate mapping: sending delta 0.22401382┬░ (was 0.22401382┬░) - visual stays synced with audio
08-02 00:35:39.608 29948 29948 D TouchSync: RAW rate mapping: sending delta 0.16750032┬░ (was 0.16750032┬░) - visual stays synced with audio
08-02 00:35:39.617 29948 29948 D TouchSync: RAW rate mapping: sending delta 0.122085184┬░ (was 0.122085184┬░) - visual stays synced with audio
08-02 00:35:39.624 29948 29948 D TouchSync: RAW rate mapping: sending delta 0.11302389┬░ (was 0.11302389┬░) - visual stays synced with audio
08-02 00:35:39.633 29948 29948 D TouchSync: RAW rate mapping: sending delta 0.058740005┬░ (was 0.058740005┬░) - visual stays synced with audio
08-02 00:35:39.641 29948 29948 D TouchSync: RAW rate mapping: sending delta -0.09782341┬░ (was -0.09782341┬░) - visual stays synced with audio
08-02 00:35:39.649 29948 29948 I AppViewModel_Touch: onPlatterTouchUp() called - releasing platter touch
08-02 00:35:39.650 29948 29948 I AppViewModel_Touch: Calling onReleasePlatterTouch()
08-02 00:35:39.650 29948 29948 I AudioEngine: TOUCH_TO_MOTOR_HANDOFF: audio_pos=9115.02, physics_rate=-0.0978, stored_rate=0.2739
08-02 00:35:39.650 29948 29948 I AudioEngine: COORDINATE_TRANSITION: TouchΓåÆMotor, Physics rate -0.0978 ΓåÆ Target 1.0000
08-02 00:35:39.650 29948 29948 I AppViewModel_Touch: onReleasePlatterTouch() completed
08-02 00:35:40.015 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=113.722626┬░, speed=0.79533184, visual_speed=1.9883296┬░/frame, delta=1.988327┬░
08-02 00:35:40.032 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=115.747925┬░, speed=0.81011826, visual_speed=2.0252957┬░/frame, delta=2.025299┬░
08-02 00:35:40.047 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=117.80754┬░, speed=0.82384545, visual_speed=2.0596137┬░/frame, delta=2.059616┬░
08-02 00:35:40.076 29948 29948 D TouchSync: === MOTOR-TO-TOUCH TRANSITION START ===
08-02 00:35:40.076 29948 29948 D TouchSync: COORDINATE_SYNC: fingerAngle=43.899616┬░, visualAngle=119.90283┬░, audioAngle=119.90283┬░
08-02 00:35:40.076 29948 29948 D TouchSync: OFFSET_ANALYSIS: visual_offset=-76.00322┬░, audio_offset=-76.00322┬░
08-02 00:35:40.076 29948 29948 D TouchSync: HANDOFF_SYNC: Motor position maintained, touch starts from audio reference frame
08-02 00:35:40.076 29948 29948 I AudioEngine: MOTOR_TO_MANUAL_SYNC: Transition detected - COORDINATE SYSTEM HANDOFF
08-02 00:35:40.076 29948 29948 I AudioEngine: COORDINATE_HANDOFF: audio_pos=4981.00, physics_rate=0.8470, stored_rate=0.8383
08-02 00:35:40.076 29948 29948 I AudioEngine: MOTOR_TO_MANUAL_SYNC: No sync performed - testing coordinate handoff
08-02 00:35:40.076 29948 29948 D TouchSync: === MOTOR-TO-TOUCH TRANSITION COMPLETE ===
08-02 00:35:40.076 29948 29948 D TouchSync: RAW rate mapping: sending delta 0.0┬░ (was 0.0┬░) - visual stays synced with audio
08-02 00:35:40.083 29948 29948 D TouchSync: RAW rate mapping: sending delta 0.6025943┬░ (was 0.6025943┬░) - visual stays synced with audio
08-02 00:35:40.091 29948 29948 D TouchSync: RAW rate mapping: sending delta 0.5605778┬░ (was 0.5605778┬░) - visual stays synced with audio
08-02 00:35:40.100 29948 29948 D TouchSync: RAW rate mapping: sending delta 0.5694226┬░ (was 0.5694226┬░) - visual stays synced with audio
08-02 00:35:40.108 29948 29948 D TouchSync: RAW rate mapping: sending delta 0.58563954┬░ (was 0.58563954┬░) - visual stays synced with audio
08-02 00:35:40.116 29948 29948 D TouchSync: RAW rate mapping: sending delta 0.4056898┬░ (was 0.4056898┬░) - visual stays synced with audio
08-02 00:35:40.119 29948 29948 I AppViewModel_Touch: onPlatterTouchUp() called - releasing platter touch
08-02 00:35:40.119 29948 29948 I AppViewModel_Touch: Calling onReleasePlatterTouch()
08-02 00:35:40.119 29948 29948 I AudioEngine: TOUCH_TO_MOTOR_HANDOFF: audio_pos=6185.20, physics_rate=0.4057, stored_rate=0.5648
08-02 00:35:40.119 29948 29948 I AudioEngine: COORDINATE_TRANSITION: TouchΓåÆMotor, Physics rate 0.4057 ΓåÆ Target 1.0000
08-02 00:35:40.119 29948 29948 I AppViewModel_Touch: onReleasePlatterTouch() completed
08-02 00:35:45.008 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=126.53528┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:35:45.026 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=129.03528┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:35:45.042 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=131.53528┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:35:50.006 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=161.53528┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:35:50.023 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=164.03528┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:35:50.040 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=166.53528┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:35:55.002 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=196.53528┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:35:55.018 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=199.03528┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:35:55.035 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=201.53528┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:36:00.002 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=226.53528┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:36:00.018 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=229.03528┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:36:00.035 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=231.53528┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:36:05.013 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=264.03528┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:36:05.031 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=266.53528┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:36:05.047 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=269.03528┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:36:10.016 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=301.53528┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:36:10.033 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=304.03528┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:36:15.000 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=334.03528┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:36:15.017 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=336.53528┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:36:15.034 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=339.03528┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:36:20.008 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=14.035278┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:36:20.024 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=16.535278┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:36:20.040 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=19.035278┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:36:25.015 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=54.03528┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:36:25.031 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=56.53528┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:36:25.048 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=59.03528┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:36:30.011 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=91.53528┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:36:30.027 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=94.03528┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░
08-02 00:36:30.044 29948 29948 D AppViewModel_Motor: MOTOR_COORDINATE: angle=96.53528┬░, speed=1.0, visual_speed=2.5┬░/frame, delta=2.5┬░

