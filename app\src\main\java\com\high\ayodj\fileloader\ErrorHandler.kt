package com.high.ayodj.fileloader

import android.util.Log

object ErrorHandler {
    private const val TAG = "FileLoaderError"

    fun logError(error: String, throwable: Throwable? = null) {
        Log.e(TAG, error, throwable)
        // Optionally, send error to analytics or crash reporting
    }

    fun userMessage(error: String): String {
        // Map internal error to user-friendly message
        return when {
            error.contains("No such file") -> "File not found."
            error.contains("Permission denied") -> "Permission denied."
            error.contains("Failed to load") -> "Could not load file."
            else -> "An unexpected error occurred."
        }
    }
}
