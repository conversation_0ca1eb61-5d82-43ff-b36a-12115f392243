#pragma once

#include <atomic>
#include <chrono>
#include <android/log.h>

#define MEMORY_TAG "MemoryMonitor"
#define MEMORY_LOGI(...) __android_log_print(ANDROID_LOG_INFO, MEMORY_TAG, __VA_ARGS__)
#define MEMORY_LOGW(...) __android_log_print(ANDROID_LOG_WARN, MEMORY_TAG, __VA_ARGS__)
#define MEMORY_LOGE(...) __android_log_print(ANDROID_LOG_ERROR, MEMORY_TAG, __VA_ARGS__)

/**
 * Memory leak detection and monitoring utility for audio applications.
 * Provides overflow-safe counters and memory usage tracking.
 */
class MemoryMonitor {
public:
    static MemoryMonitor& getInstance() {
        static MemoryMonitor instance;
        return instance;
    }

    // Overflow-safe counter that resets before overflow
    class SafeCounter {
    private:
        std::atomic<uint32_t> counter_{0};
        const uint32_t resetThreshold_;
        
    public:
        explicit SafeCounter(uint32_t resetThreshold = 0x7FFFFFFF) 
            : resetThreshold_(resetThreshold) {}
        
        uint32_t increment() {
            uint32_t current = counter_.fetch_add(1);
            if (current >= resetThreshold_) {
                counter_.store(0);
                return 0;
            }
            return current;
        }
        
        uint32_t get() const { return counter_.load(); }
        void reset() { counter_.store(0); }
    };

    // Memory usage tracking
    struct MemoryStats {
        std::atomic<size_t> audioBufferAllocations{0};
        std::atomic<size_t> cacheSize{0};
        std::atomic<size_t> totalAllocatedBytes{0};
        std::atomic<uint64_t> audioCallbackCount{0};
        std::atomic<uint64_t> lastReportTime{0};
    };

    MemoryStats& getStats() { return stats_; }

    // Report memory usage periodically (call from audio callback)
    void reportIfNeeded() {
        uint64_t now = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now().time_since_epoch()).count();
        
        uint64_t lastReport = stats_.lastReportTime.load();
        if (now - lastReport > 30000) { // Report every 30 seconds
            if (stats_.lastReportTime.compare_exchange_weak(lastReport, now)) {
                MEMORY_LOGI("Memory Stats - Buffers: %zu, Cache: %zu, Total: %zu MB, Callbacks: %llu",
                    stats_.audioBufferAllocations.load(),
                    stats_.cacheSize.load(),
                    stats_.totalAllocatedBytes.load() / (1024 * 1024),
                    stats_.audioCallbackCount.load());
            }
        }
    }

    // Track audio callback invocations
    void incrementAudioCallback() {
        stats_.audioCallbackCount.fetch_add(1);
        reportIfNeeded();
    }

    // Track memory allocations
    void trackAllocation(size_t bytes) {
        stats_.totalAllocatedBytes.fetch_add(bytes);
        stats_.audioBufferAllocations.fetch_add(1);
    }

    void trackDeallocation(size_t bytes) {
        stats_.totalAllocatedBytes.fetch_sub(bytes);
        if (stats_.audioBufferAllocations.load() > 0) {
            stats_.audioBufferAllocations.fetch_sub(1);
        }
    }

    void updateCacheSize(size_t newSize) {
        stats_.cacheSize.store(newSize);
    }

private:
    MemoryStats stats_;
    MemoryMonitor() = default;
};

// Convenience macros for safe logging with overflow protection
#define SAFE_LOG_EVERY_N(counter, n, level, ...) \
    do { \
        static MemoryMonitor::SafeCounter safeCounter; \
        if ((safeCounter.increment() & ((n) - 1)) == 0) { \
            __android_log_print(level, APP_TAG, __VA_ARGS__); \
        } \
    } while(0)

#define SAFE_LOG_INFO_EVERY_N(n, ...) SAFE_LOG_EVERY_N(counter, n, ANDROID_LOG_INFO, __VA_ARGS__)
#define SAFE_LOG_ERROR_EVERY_N(n, ...) SAFE_LOG_EVERY_N(counter, n, ANDROID_LOG_ERROR, __VA_ARGS__)
#define SAFE_LOG_WARN_EVERY_N(n, ...) SAFE_LOG_EVERY_N(counter, n, ANDROID_LOG_WARN, __VA_ARGS__)
