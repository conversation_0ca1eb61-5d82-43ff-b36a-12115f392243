package com.high.ayodj

import android.app.Application
import android.content.Intent
import androidx.activity.result.ActivityResultLauncher
import org.junit.Test
import org.junit.Assert.*
import org.junit.Before
import org.mockito.Mockito
import org.mockito.Mockito.mock

class AppViewModelTest {

    private lateinit var viewModel: AppViewModel
    private lateinit var billingClientWrapper: BillingClientWrapper

    @Before
    fun setup() {
        MainActivity.isCurrentUserPremium = false

        viewModel = AppViewModel(
            application = Mockito.mock(Application::class.java),
            cloudSyncManager = Mockito.mock(CloudSyncManager::class.java),
            signInLauncher = Mockito.mock(ActivityResultLauncher::class.java) as ActivityResultLauncher<Intent>,
            onPlayIntroAndLoopOnPlatter = {},
            onLoadUserPlatterSample = { _, _ -> },
            onLoadAssetPlatterSample = {},
            onStopMusicTrack = {},
            onLoadUserMusicTrack = { _, _ -> },
            onLoadAssetMusicTrack = {},
            onUpdatePlatterFaderVolume = {},
            onUpdateMusicMasterVolume = {},
            onScratchPlatterActive = { _, _ -> },
            onReleasePlatterTouch = {},

            onSetAudioNormalizationFactor = {},
            onOpenFilePicker = {}
        )
    }

    @Test
    fun initialShowSubscribePopup_isFalse() {
        assertFalse("Initially, showSubscribePopup should be false", viewModel.showSubscribePopup)
    }

    @Test
    fun handleButton1Hold_premiumUser_doesNotShowPopup() {
        MainActivity.isCurrentUserPremium = true
        viewModel.handleButton1Hold()
        assertFalse("Popup should NOT show for premium user on Button 1 Hold", viewModel.showSubscribePopup)
    }

    @Test
    fun handleButton1Hold_nonPremiumUser_showsPopup() {
        MainActivity.isCurrentUserPremium = false
        viewModel.handleButton1Hold()
        assertTrue("Popup SHOULD show for non-premium user on Button 1 Hold", viewModel.showSubscribePopup)
    }

    @Test
    fun handleButton2Hold_premiumUser_doesNotShowPopup() {
        MainActivity.isCurrentUserPremium = true
        viewModel.handleButton2Hold()
        assertFalse("Popup should NOT show for premium user on Button 2 Hold", viewModel.showSubscribePopup)
    }

    @Test
    fun handleButton2Hold_nonPremiumUser_showsPopup() {
        MainActivity.isCurrentUserPremium = false
        viewModel.handleButton2Hold()
        assertTrue("Popup SHOULD show for non-premium user on Button 2 Hold", viewModel.showSubscribePopup)
    }

    @Test
    fun settingsSwitch_canTogglePremiumStatus() {
        assertFalse("Initial premium status should be false (from setup)", MainActivity.isCurrentUserPremium)
        MainActivity.isCurrentUserPremium = true
        assertTrue("Premium status should be true after direct modification", MainActivity.isCurrentUserPremium)
        MainActivity.isCurrentUserPremium = false
        assertFalse("Premium status should be false after direct modification back", MainActivity.isCurrentUserPremium)
    }
}
