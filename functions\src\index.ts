import {onCall} from "firebase-functions/v2/https";
import * as admin from "firebase-admin";
import {google} from "googleapis";
import * as serviceAccount from "./ayodj-464105-c4c22b51616f.json";

admin.initializeApp();

const androidpublisher = google.androidpublisher("v3");
const oauth2Client = new google.auth.OAuth2(
  process.env.GOOGLE_CLIENT_ID,
  process.env.GOOGLE_CLIENT_SECRET,
  "postmessage"
);

google.options({
  auth: new google.auth.GoogleAuth({
    credentials: {
      client_email: serviceAccount.client_email,
      private_key: serviceAccount.private_key,
    },
    scopes: ["https://www.googleapis.com/auth/androidpublisher"],
  }),
});

exports.getTokens = onCall(async (request) => {
  const code = request.data.code;
  const userId = request.auth?.uid;

  if (!userId) {
    throw new Error("User is not authenticated.");
  }

  if (!code) {
    throw new Error("Authorization code is required.");
  }

  try {
    const {tokens} = await oauth2Client.getToken(code);
    if (tokens.refresh_token) {
      await admin.firestore().collection("users").doc(userId).set({
        refreshToken: tokens.refresh_token,
      }, {merge: true});
    }
    return {
      message: "Tokens retrieved and stored successfully.",
      tokens: tokens,
    };
  } catch (error) {
    console.error("Failed to retrieve tokens:", error);
    throw new Error("Failed to retrieve tokens.");
  }
});

exports.verifyPurchase = onCall(async (request) => {
  const {packageName, subscriptionId, token} = request.data;

  if (!packageName || !subscriptionId || !token) {
    throw new Error("Missing required parameters.");
  }

  try {
    const response = await androidpublisher.purchases.subscriptions.get({
      packageName: packageName,
      subscriptionId: subscriptionId,
      token: token,
    });

    return {
      message: "Purchase verified successfully.",
      data: response.data,
    };
  } catch (error) {
    console.error("Purchase verification failed:", error);
    throw new Error("Purchase verification failed.");
  }
});
